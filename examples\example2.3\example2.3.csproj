﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>net8.0</TargetFramework>
		<RootNamespace>example2._3</RootNamespace>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="TorchSharp" Version="0.103.1" />
		<PackageReference Include="TorchSharp-cuda-windows" Version="0.103.1" />
		<PackageReference Include="TorchVision" Version="0.103.1" />
	</ItemGroup>

	<ItemGroup>
		<!--<ProjectReference Include="..\..\src\Maomi.ScottPlot.Winforms\Maomi.ScottPlot.Winforms.csproj" />-->
		<ProjectReference Include="..\..\src\Maomi.Torch\Maomi.Torch.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <None Update="5.jpg">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	  <None Update="6.jpg">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	</ItemGroup>

</Project>
