<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\FloorPlanGAN.Core\FloorPlanGAN.Core.csproj" />
    <ProjectReference Include="..\..\src\FloorPlanGAN.Models\FloorPlanGAN.Models.csproj" />
    <ProjectReference Include="..\..\src\FloorPlanGAN.Data\FloorPlanGAN.Data.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="data\**\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
