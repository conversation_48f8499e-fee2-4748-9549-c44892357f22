using System.Drawing;
using Newtonsoft.Json;

namespace FloorPlanGAN.Data.Models;

/// <summary>
/// 楼层轮廓数据
/// </summary>
public class FloorContour
{
    /// <summary>
    /// 楼层编号
    /// </summary>
    public int FloorNumber { get; set; }
    
    /// <summary>
    /// 楼层名称
    /// </summary>
    public string FloorName { get; set; } = string.Empty;
    
    /// <summary>
    /// 外轮廓点集合
    /// </summary>
    public List<PointF> OuterContour { get; set; } = new();
    
    /// <summary>
    /// 内轮廓点集合（如天井、中庭等）
    /// </summary>
    public List<List<PointF>> InnerContours { get; set; } = new();
    
    /// <summary>
    /// 楼层面积（平方米）
    /// </summary>
    public float Area { get; set; }
    
    /// <summary>
    /// 楼层周长（米）
    /// </summary>
    public float Perimeter { get; set; }
    
    /// <summary>
    /// 边界框
    /// </summary>
    public RectangleF BoundingBox { get; set; }
    
    /// <summary>
    /// 楼层类型
    /// </summary>
    public FloorType FloorType { get; set; } = FloorType.Residential;
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    
    /// <summary>
    /// 计算面积
    /// </summary>
    public float CalculateArea()
    {
        if (OuterContour.Count < 3) return 0;
        
        // 使用鞋带公式计算多边形面积
        float area = 0;
        for (int i = 0; i < OuterContour.Count; i++)
        {
            int j = (i + 1) % OuterContour.Count;
            area += OuterContour[i].X * OuterContour[j].Y;
            area -= OuterContour[j].X * OuterContour[i].Y;
        }
        area = Math.Abs(area) / 2.0f;
        
        // 减去内轮廓面积
        foreach (var innerContour in InnerContours)
        {
            if (innerContour.Count < 3) continue;
            
            float innerArea = 0;
            for (int i = 0; i < innerContour.Count; i++)
            {
                int j = (i + 1) % innerContour.Count;
                innerArea += innerContour[i].X * innerContour[j].Y;
                innerArea -= innerContour[j].X * innerContour[i].Y;
            }
            area -= Math.Abs(innerArea) / 2.0f;
        }
        
        Area = area;
        return area;
    }
    
    /// <summary>
    /// 计算周长
    /// </summary>
    public float CalculatePerimeter()
    {
        if (OuterContour.Count < 2) return 0;
        
        float perimeter = 0;
        for (int i = 0; i < OuterContour.Count; i++)
        {
            int j = (i + 1) % OuterContour.Count;
            float dx = OuterContour[j].X - OuterContour[i].X;
            float dy = OuterContour[j].Y - OuterContour[i].Y;
            perimeter += (float)Math.Sqrt(dx * dx + dy * dy);
        }
        
        Perimeter = perimeter;
        return perimeter;
    }
    
    /// <summary>
    /// 计算边界框
    /// </summary>
    public RectangleF CalculateBoundingBox()
    {
        if (OuterContour.Count == 0)
        {
            BoundingBox = RectangleF.Empty;
            return BoundingBox;
        }
        
        float minX = OuterContour[0].X;
        float minY = OuterContour[0].Y;
        float maxX = OuterContour[0].X;
        float maxY = OuterContour[0].Y;
        
        foreach (var point in OuterContour)
        {
            minX = Math.Min(minX, point.X);
            minY = Math.Min(minY, point.Y);
            maxX = Math.Max(maxX, point.X);
            maxY = Math.Max(maxY, point.Y);
        }
        
        BoundingBox = new RectangleF(minX, minY, maxX - minX, maxY - minY);
        return BoundingBox;
    }
    
    /// <summary>
    /// 标准化轮廓到指定尺寸
    /// </summary>
    public FloorContour Normalize(int targetSize)
    {
        var normalized = new FloorContour
        {
            FloorNumber = FloorNumber,
            FloorName = FloorName,
            FloorType = FloorType,
            CreatedAt = CreatedAt
        };
        
        var bbox = CalculateBoundingBox();
        if (bbox.Width == 0 || bbox.Height == 0) return normalized;
        
        float scale = Math.Min(targetSize / bbox.Width, targetSize / bbox.Height);
        float offsetX = (targetSize - bbox.Width * scale) / 2 - bbox.X * scale;
        float offsetY = (targetSize - bbox.Height * scale) / 2 - bbox.Y * scale;
        
        // 标准化外轮廓
        normalized.OuterContour = OuterContour.Select(p => new PointF(
            p.X * scale + offsetX,
            p.Y * scale + offsetY
        )).ToList();
        
        // 标准化内轮廓
        normalized.InnerContours = InnerContours.Select(contour =>
            contour.Select(p => new PointF(
                p.X * scale + offsetX,
                p.Y * scale + offsetY
            )).ToList()
        ).ToList();
        
        normalized.CalculateArea();
        normalized.CalculatePerimeter();
        normalized.CalculateBoundingBox();
        
        return normalized;
    }
    
    /// <summary>
    /// 转换为JSON字符串
    /// </summary>
    public string ToJson()
    {
        return JsonConvert.SerializeObject(this, Formatting.Indented);
    }
    
    /// <summary>
    /// 从JSON字符串创建
    /// </summary>
    public static FloorContour? FromJson(string json)
    {
        return JsonConvert.DeserializeObject<FloorContour>(json);
    }
}

/// <summary>
/// 楼层类型
/// </summary>
public enum FloorType
{
    /// <summary>
    /// 住宅
    /// </summary>
    Residential,
    
    /// <summary>
    /// 办公
    /// </summary>
    Office,
    
    /// <summary>
    /// 商业
    /// </summary>
    Commercial,
    
    /// <summary>
    /// 工业
    /// </summary>
    Industrial,
    
    /// <summary>
    /// 混合用途
    /// </summary>
    Mixed,
    
    /// <summary>
    /// 其他
    /// </summary>
    Other
}
