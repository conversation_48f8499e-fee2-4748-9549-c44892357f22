<ui:Window x:Class="FloorPlanGAN.Studio.Views.MainWindow"
           xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
           xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
           xmlns:ui="http://schemas.modernwpf.com/2019"
           xmlns:vm="clr-namespace:FloorPlanGAN.Studio.ViewModels"
           Title="FloorPlanGAN Studio - 智能建筑平面图生成工具"
           Width="1400" Height="900"
           MinWidth="1200" MinHeight="700"
           WindowStartupLocation="CenterScreen"
           ui:WindowHelper.UseModernWindowStyle="True">
    
    <Window.DataContext>
        <vm:MainViewModel />
    </Window.DataContext>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        
        <!-- 菜单栏 -->
        <Menu Grid.Row="0" Background="{DynamicResource SystemControlBackgroundChromeMediumLowBrush}">
            <MenuItem Header="文件(_F)">
                <MenuItem Header="新建项目(_N)" Command="{Binding NewProjectCommand}" InputGestureText="Ctrl+N" />
                <MenuItem Header="打开项目(_O)" Command="{Binding OpenProjectCommand}" InputGestureText="Ctrl+O" />
                <MenuItem Header="保存项目(_S)" Command="{Binding SaveProjectCommand}" InputGestureText="Ctrl+S" />
                <MenuItem Header="另存为(_A)" Command="{Binding SaveAsProjectCommand}" InputGestureText="Ctrl+Shift+S" />
                <Separator />
                <MenuItem Header="导入轮廓(_I)" Command="{Binding ImportContourCommand}" />
                <MenuItem Header="导出结果(_E)" Command="{Binding ExportResultCommand}" />
                <Separator />
                <MenuItem Header="退出(_X)" Command="{Binding ExitCommand}" InputGestureText="Alt+F4" />
            </MenuItem>
            
            <MenuItem Header="编辑(_E)">
                <MenuItem Header="撤销(_U)" Command="{Binding UndoCommand}" InputGestureText="Ctrl+Z" />
                <MenuItem Header="重做(_R)" Command="{Binding RedoCommand}" InputGestureText="Ctrl+Y" />
                <Separator />
                <MenuItem Header="清除轮廓(_C)" Command="{Binding ClearContourCommand}" />
                <MenuItem Header="重置视图(_V)" Command="{Binding ResetViewCommand}" />
            </MenuItem>
            
            <MenuItem Header="模型(_M)">
                <MenuItem Header="训练模型(_T)" Command="{Binding StartTrainingCommand}" />
                <MenuItem Header="加载模型(_L)" Command="{Binding LoadModelCommand}" />
                <MenuItem Header="模型设置(_S)" Command="{Binding ModelSettingsCommand}" />
            </MenuItem>
            
            <MenuItem Header="工具(_T)">
                <MenuItem Header="设备信息(_D)" Command="{Binding ShowDeviceInfoCommand}" />
                <MenuItem Header="清理缓存(_C)" Command="{Binding ClearCacheCommand}" />
                <MenuItem Header="设置(_S)" Command="{Binding ShowSettingsCommand}" />
            </MenuItem>
            
            <MenuItem Header="帮助(_H)">
                <MenuItem Header="用户手册(_U)" Command="{Binding ShowHelpCommand}" />
                <MenuItem Header="关于(_A)" Command="{Binding ShowAboutCommand}" />
            </MenuItem>
        </Menu>
        
        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300" MinWidth="250" />
                <ColumnDefinition Width="5" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="5" />
                <ColumnDefinition Width="350" MinWidth="300" />
            </Grid.ColumnDefinitions>
            
            <!-- 左侧面板 - 项目管理 -->
            <Border Grid.Column="0" Background="{DynamicResource SystemControlBackgroundChromeMediumLowBrush}" 
                    BorderBrush="{DynamicResource SystemControlForegroundBaseMediumLowBrush}" BorderThickness="0,0,1,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <!-- 项目信息 -->
                        <ui:SimpleStackPanel Spacing="10">
                            <TextBlock Text="项目信息" Style="{DynamicResource SubtitleTextBlockStyle}" />
                            
                            <TextBlock Text="项目名称:" />
                            <TextBox Text="{Binding ProjectName, UpdateSourceTrigger=PropertyChanged}" />
                            
                            <TextBlock Text="楼层数量:" />
                            <ui:NumberBox Value="{Binding FloorCount, UpdateSourceTrigger=PropertyChanged}" 
                                         Minimum="1" Maximum="50" SpinButtonPlacementMode="Inline" />
                            
                            <TextBlock Text="项目描述:" />
                            <TextBox Text="{Binding ProjectDescription, UpdateSourceTrigger=PropertyChanged}" 
                                    TextWrapping="Wrap" AcceptsReturn="True" Height="80" />
                        </ui:SimpleStackPanel>
                        
                        <Separator Margin="0,20" />
                        
                        <!-- 楼层列表 -->
                        <ui:SimpleStackPanel Spacing="10">
                            <TextBlock Text="楼层管理" Style="{DynamicResource SubtitleTextBlockStyle}" />
                            
                            <Button Content="添加楼层" Command="{Binding AddFloorCommand}" 
                                   HorizontalAlignment="Stretch" />
                            
                            <ListView ItemsSource="{Binding Floors}" SelectedItem="{Binding SelectedFloor}"
                                     Height="200" BorderThickness="1" 
                                     BorderBrush="{DynamicResource SystemControlForegroundBaseMediumLowBrush}">
                                <ListView.ItemTemplate>
                                    <DataTemplate>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="Auto" />
                                            </Grid.ColumnDefinitions>
                                            
                                            <StackPanel Grid.Column="0">
                                                <TextBlock Text="{Binding FloorName}" FontWeight="Bold" />
                                                <TextBlock Text="{Binding FloorNumber, StringFormat='第 {0} 层'}" 
                                                          Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}" />
                                            </StackPanel>
                                            
                                            <Button Grid.Column="1" Content="×" Width="20" Height="20"
                                                   Command="{Binding DataContext.RemoveFloorCommand, RelativeSource={RelativeSource AncestorType=ListView}}"
                                                   CommandParameter="{Binding}" />
                                        </Grid>
                                    </DataTemplate>
                                </ListView.ItemTemplate>
                            </ListView>
                        </ui:SimpleStackPanel>
                    </StackPanel>
                </ScrollViewer>
            </Border>
            
            <!-- 分隔符 -->
            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" 
                         Background="{DynamicResource SystemControlForegroundBaseMediumLowBrush}" />
            
            <!-- 中央区域 - 轮廓编辑和预览 -->
            <TabView Grid.Column="2" TabWidthMode="Equal">
                <TabViewItem Header="轮廓编辑">
                    <Grid>
                        <!-- 轮廓编辑器将在这里实现 -->
                        <Border Background="{DynamicResource SystemControlBackgroundChromeMediumLowBrush}"
                               BorderBrush="{DynamicResource SystemControlForegroundBaseMediumLowBrush}" 
                               BorderThickness="1">
                            <TextBlock Text="轮廓编辑器" HorizontalAlignment="Center" VerticalAlignment="Center"
                                      Style="{DynamicResource SubtitleTextBlockStyle}" />
                        </Border>
                    </Grid>
                </TabViewItem>
                
                <TabViewItem Header="生成预览">
                    <Grid>
                        <!-- 生成结果预览将在这里实现 -->
                        <Border Background="{DynamicResource SystemControlBackgroundChromeMediumLowBrush}"
                               BorderBrush="{DynamicResource SystemControlForegroundBaseMediumLowBrush}" 
                               BorderThickness="1">
                            <TextBlock Text="生成结果预览" HorizontalAlignment="Center" VerticalAlignment="Center"
                                      Style="{DynamicResource SubtitleTextBlockStyle}" />
                        </Border>
                    </Grid>
                </TabViewItem>
            </TabView>
            
            <!-- 分隔符 -->
            <GridSplitter Grid.Column="3" HorizontalAlignment="Stretch" 
                         Background="{DynamicResource SystemControlForegroundBaseMediumLowBrush}" />
            
            <!-- 右侧面板 - 生成控制 -->
            <Border Grid.Column="4" Background="{DynamicResource SystemControlBackgroundChromeMediumLowBrush}" 
                    BorderBrush="{DynamicResource SystemControlForegroundBaseMediumLowBrush}" BorderThickness="1,0,0,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <!-- 生成控制 -->
                        <ui:SimpleStackPanel Spacing="10">
                            <TextBlock Text="生成控制" Style="{DynamicResource SubtitleTextBlockStyle}" />
                            
                            <Button Content="生成平面图" Command="{Binding GenerateCommand}" 
                                   HorizontalAlignment="Stretch" Height="40"
                                   Style="{DynamicResource AccentButtonStyle}" />
                            
                            <TextBlock Text="生成参数:" />
                            
                            <TextBlock Text="随机种子:" />
                            <ui:NumberBox Value="{Binding GenerationSeed, UpdateSourceTrigger=PropertyChanged}" 
                                         Minimum="0" Maximum="999999" SpinButtonPlacementMode="Inline" />
                            
                            <TextBlock Text="生成数量:" />
                            <ui:NumberBox Value="{Binding GenerationCount, UpdateSourceTrigger=PropertyChanged}" 
                                         Minimum="1" Maximum="10" SpinButtonPlacementMode="Inline" />
                            
                            <CheckBox Content="使用随机种子" IsChecked="{Binding UseRandomSeed}" />
                        </ui:SimpleStackPanel>
                        
                        <Separator Margin="0,20" />
                        
                        <!-- 模型状态 -->
                        <ui:SimpleStackPanel Spacing="10">
                            <TextBlock Text="模型状态" Style="{DynamicResource SubtitleTextBlockStyle}" />
                            
                            <TextBlock Text="{Binding ModelStatus}" />
                            <ProgressBar Value="{Binding ModelLoadProgress}" Maximum="100" Height="6" />
                            
                            <Button Content="加载模型" Command="{Binding LoadModelCommand}" 
                                   HorizontalAlignment="Stretch" />
                        </ui:SimpleStackPanel>
                        
                        <Separator Margin="0,20" />
                        
                        <!-- 训练控制 -->
                        <ui:SimpleStackPanel Spacing="10">
                            <TextBlock Text="模型训练" Style="{DynamicResource SubtitleTextBlockStyle}" />
                            
                            <Button Content="开始训练" Command="{Binding StartTrainingCommand}" 
                                   HorizontalAlignment="Stretch" />
                            
                            <TextBlock Text="训练进度:" />
                            <ProgressBar Value="{Binding TrainingProgress}" Maximum="100" Height="6" />
                            
                            <TextBlock Text="{Binding TrainingStatus}" TextWrapping="Wrap" />
                        </ui:SimpleStackPanel>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>
        
        <!-- 状态栏 -->
        <StatusBar Grid.Row="2" Background="{DynamicResource SystemControlBackgroundChromeMediumLowBrush}">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" />
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Text="{Binding DeviceInfo}" />
            </StatusBarItem>
        </StatusBar>
    </Grid>
</ui:Window>
