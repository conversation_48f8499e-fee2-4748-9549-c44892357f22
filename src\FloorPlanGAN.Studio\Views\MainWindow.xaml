<Window x:Class="FloorPlanGAN.Studio.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="clr-namespace:FloorPlanGAN.Studio.ViewModels"
        Title="FloorPlanGAN Studio - 智能建筑平面图生成工具"
        Width="1400" Height="900"
        MinWidth="1200" MinHeight="700"
        WindowStartupLocation="CenterScreen">
    
    <Window.DataContext>
        <vm:MainViewModel />
    </Window.DataContext>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        
        <!-- 菜单栏 -->
        <Menu Grid.Row="0" Background="LightGray">
            <MenuItem Header="文件(_F)">
                <MenuItem Header="新建项目(_N)" Command="{Binding NewProjectCommand}" InputGestureText="Ctrl+N" />
                <MenuItem Header="打开项目(_O)" Command="{Binding OpenProjectCommand}" InputGestureText="Ctrl+O" />
                <MenuItem Header="保存项目(_S)" Command="{Binding SaveProjectCommand}" InputGestureText="Ctrl+S" />
                <MenuItem Header="另存为(_A)" Command="{Binding SaveAsProjectCommand}" InputGestureText="Ctrl+Shift+S" />
                <Separator />
                <MenuItem Header="导入轮廓(_I)" Command="{Binding ImportContourCommand}" />
                <MenuItem Header="导出结果(_E)" Command="{Binding ExportResultCommand}" />
                <Separator />
                <MenuItem Header="退出(_X)" Command="{Binding ExitCommand}" InputGestureText="Alt+F4" />
            </MenuItem>
            
            <MenuItem Header="编辑(_E)">
                <MenuItem Header="撤销(_U)" Command="{Binding UndoCommand}" InputGestureText="Ctrl+Z" />
                <MenuItem Header="重做(_R)" Command="{Binding RedoCommand}" InputGestureText="Ctrl+Y" />
                <Separator />
                <MenuItem Header="清除轮廓(_C)" Command="{Binding ClearContourCommand}" />
                <MenuItem Header="重置视图(_V)" Command="{Binding ResetViewCommand}" />
            </MenuItem>
            
            <MenuItem Header="模型(_M)">
                <MenuItem Header="训练模型(_T)" Command="{Binding StartTrainingCommand}" />
                <MenuItem Header="加载模型(_L)" Command="{Binding LoadModelCommand}" />
                <MenuItem Header="模型设置(_S)" Command="{Binding ModelSettingsCommand}" />
            </MenuItem>
            
            <MenuItem Header="工具(_T)">
                <MenuItem Header="设备信息(_D)" Command="{Binding ShowDeviceInfoCommand}" />
                <MenuItem Header="清理缓存(_C)" Command="{Binding ClearCacheCommand}" />
                <MenuItem Header="设置(_S)" Command="{Binding ShowSettingsCommand}" />
            </MenuItem>
            
            <MenuItem Header="帮助(_H)">
                <MenuItem Header="用户手册(_U)" Command="{Binding ShowHelpCommand}" />
                <MenuItem Header="关于(_A)" Command="{Binding ShowAboutCommand}" />
            </MenuItem>
        </Menu>
        
        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300" MinWidth="250" />
                <ColumnDefinition Width="5" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="5" />
                <ColumnDefinition Width="350" MinWidth="300" />
            </Grid.ColumnDefinitions>
            
            <!-- 左侧面板 - 项目管理 -->
            <Border Grid.Column="0" Background="WhiteSmoke"
                    BorderBrush="Gray" BorderThickness="0,0,1,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <!-- 项目信息 -->
                        <StackPanel>
                            <TextBlock Text="项目信息" FontSize="16" FontWeight="Bold" Margin="0,0,0,10" />

                            <TextBlock Text="项目名称:" Margin="0,5,0,2" />
                            <TextBox Text="{Binding ProjectName, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10" />

                            <TextBlock Text="楼层数量:" Margin="0,5,0,2" />
                            <TextBox Text="{Binding FloorCount, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10" />

                            <TextBlock Text="项目描述:" Margin="0,5,0,2" />
                            <TextBox Text="{Binding ProjectDescription, UpdateSourceTrigger=PropertyChanged}"
                                    TextWrapping="Wrap" AcceptsReturn="True" Height="80" Margin="0,0,0,10" />
                        </StackPanel>
                        
                        <Separator Margin="0,20" />
                        
                        <!-- 楼层列表 -->
                        <StackPanel Margin="0,20,0,0">
                            <TextBlock Text="楼层管理" FontSize="16" FontWeight="Bold" Margin="0,0,0,10" />

                            <Button Content="添加楼层" Command="{Binding AddFloorCommand}"
                                   HorizontalAlignment="Stretch" Margin="0,0,0,10" />

                            <ListView ItemsSource="{Binding Floors}" SelectedItem="{Binding SelectedFloor}"
                                     Height="200" BorderThickness="1" BorderBrush="Gray">
                                <ListView.ItemTemplate>
                                    <DataTemplate>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="Auto" />
                                            </Grid.ColumnDefinitions>
                                            
                                            <StackPanel Grid.Column="0">
                                                <TextBlock Text="{Binding FloorName}" FontWeight="Bold" />
                                                <TextBlock Text="{Binding FloorNumber, StringFormat='第 {0} 层'}"
                                                          Foreground="Gray" />
                                            </StackPanel>
                                            
                                            <Button Grid.Column="1" Content="×" Width="20" Height="20"
                                                   Command="{Binding DataContext.RemoveFloorCommand, RelativeSource={RelativeSource AncestorType=ListView}}"
                                                   CommandParameter="{Binding}" />
                                        </Grid>
                                    </DataTemplate>
                                </ListView.ItemTemplate>
                            </ListView>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </Border>
            
            <!-- 分隔符 -->
            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" Background="Gray" />
            
            <!-- 中央区域 - 轮廓编辑和预览 -->
            <TabControl Grid.Column="2">
                <TabItem Header="轮廓编辑">
                    <Grid>
                        <!-- 轮廓编辑器将在这里实现 -->
                        <Border Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="1">
                            <TextBlock Text="轮廓编辑器" HorizontalAlignment="Center" VerticalAlignment="Center"
                                      FontSize="16" FontWeight="Bold" />
                        </Border>
                    </Grid>
                </TabItem>

                <TabItem Header="生成预览">
                    <Grid>
                        <!-- 生成结果预览将在这里实现 -->
                        <Border Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="1">
                            <TextBlock Text="生成结果预览" HorizontalAlignment="Center" VerticalAlignment="Center"
                                      FontSize="16" FontWeight="Bold" />
                        </Border>
                    </Grid>
                </TabItem>
            </TabControl>
            
            <!-- 分隔符 -->
            <GridSplitter Grid.Column="3" HorizontalAlignment="Stretch" Background="Gray" />
            
            <!-- 右侧面板 - 生成控制 -->
            <Border Grid.Column="4" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="1,0,0,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <!-- 生成控制 -->
                        <StackPanel>
                            <TextBlock Text="生成控制" FontSize="16" FontWeight="Bold" Margin="0,0,0,10" />

                            <Button Content="生成平面图" Command="{Binding GenerateCommand}"
                                   HorizontalAlignment="Stretch" Height="40" Background="DodgerBlue"
                                   Foreground="White" Margin="0,0,0,15" />

                            <TextBlock Text="生成参数:" FontWeight="Bold" Margin="0,0,0,10" />

                            <TextBlock Text="随机种子:" Margin="0,5,0,2" />
                            <TextBox Text="{Binding GenerationSeed, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10" />

                            <TextBlock Text="生成数量:" Margin="0,5,0,2" />
                            <TextBox Text="{Binding GenerationCount, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10" />

                            <CheckBox Content="使用随机种子" IsChecked="{Binding UseRandomSeed}" Margin="0,0,0,10" />
                        </StackPanel>
                        
                        <Separator Margin="0,20" />
                        
                        <!-- 模型状态 -->
                        <StackPanel Margin="0,20,0,0">
                            <TextBlock Text="模型状态" FontSize="16" FontWeight="Bold" Margin="0,0,0,10" />

                            <TextBlock Text="{Binding ModelStatus}" Margin="0,0,0,5" />
                            <ProgressBar Value="{Binding ModelLoadProgress}" Maximum="100" Height="6" Margin="0,0,0,10" />

                            <Button Content="加载模型" Command="{Binding LoadModelCommand}"
                                   HorizontalAlignment="Stretch" Margin="0,0,0,10" />
                        </StackPanel>
                        
                        <Separator Margin="0,20" />
                        
                        <!-- 训练控制 -->
                        <StackPanel Margin="0,20,0,0">
                            <TextBlock Text="模型训练" FontSize="16" FontWeight="Bold" Margin="0,0,0,10" />

                            <Button Content="开始训练" Command="{Binding StartTrainingCommand}"
                                   HorizontalAlignment="Stretch" Margin="0,0,0,10" />

                            <TextBlock Text="训练进度:" Margin="0,5,0,2" />
                            <ProgressBar Value="{Binding TrainingProgress}" Maximum="100" Height="6" Margin="0,0,0,10" />

                            <TextBlock Text="{Binding TrainingStatus}" TextWrapping="Wrap" />
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>
        
        <!-- 状态栏 -->
        <StatusBar Grid.Row="2" Background="LightGray">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" />
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Text="{Binding DeviceInfo}" />
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
