using Newtonsoft.Json;

namespace FloorPlanGAN.Data.Models;

/// <summary>
/// 建筑项目数据
/// </summary>
public class BuildingProject
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public Guid ProjectId { get; set; } = Guid.NewGuid();
    
    /// <summary>
    /// 项目名称
    /// </summary>
    public string ProjectName { get; set; } = string.Empty;
    
    /// <summary>
    /// 项目描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// 楼层数量
    /// </summary>
    public int FloorCount { get; set; }
    
    /// <summary>
    /// 楼层轮廓列表
    /// </summary>
    public List<FloorContour> FloorContours { get; set; } = new();
    
    /// <summary>
    /// 生成的平面图列表
    /// </summary>
    public List<GeneratedFloorPlan> GeneratedPlans { get; set; } = new();
    
    /// <summary>
    /// 项目创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    
    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime LastModified { get; set; } = DateTime.Now;
    
    /// <summary>
    /// 项目作者
    /// </summary>
    public string Author { get; set; } = Environment.UserName;
    
    /// <summary>
    /// 项目版本
    /// </summary>
    public string Version { get; set; } = "1.0.0";
    
    /// <summary>
    /// 项目标签
    /// </summary>
    public List<string> Tags { get; set; } = new();
    
    /// <summary>
    /// 添加楼层轮廓
    /// </summary>
    public void AddFloorContour(FloorContour contour)
    {
        FloorContours.Add(contour);
        FloorCount = FloorContours.Count;
        LastModified = DateTime.Now;
    }
    
    /// <summary>
    /// 移除楼层轮廓
    /// </summary>
    public bool RemoveFloorContour(int floorNumber)
    {
        var contour = FloorContours.FirstOrDefault(f => f.FloorNumber == floorNumber);
        if (contour != null)
        {
            FloorContours.Remove(contour);
            FloorCount = FloorContours.Count;
            LastModified = DateTime.Now;
            return true;
        }
        return false;
    }
    
    /// <summary>
    /// 获取楼层轮廓
    /// </summary>
    public FloorContour? GetFloorContour(int floorNumber)
    {
        return FloorContours.FirstOrDefault(f => f.FloorNumber == floorNumber);
    }
    
    /// <summary>
    /// 添加生成的平面图
    /// </summary>
    public void AddGeneratedPlan(GeneratedFloorPlan plan)
    {
        // 移除同楼层的旧版本
        GeneratedPlans.RemoveAll(p => p.FloorNumber == plan.FloorNumber);
        GeneratedPlans.Add(plan);
        LastModified = DateTime.Now;
    }
    
    /// <summary>
    /// 获取生成的平面图
    /// </summary>
    public GeneratedFloorPlan? GetGeneratedPlan(int floorNumber)
    {
        return GeneratedPlans.FirstOrDefault(p => p.FloorNumber == floorNumber);
    }
    
    /// <summary>
    /// 验证项目数据完整性
    /// </summary>
    public ValidationResult Validate()
    {
        var result = new ValidationResult();
        
        if (string.IsNullOrWhiteSpace(ProjectName))
        {
            result.Errors.Add("项目名称不能为空");
        }
        
        if (FloorCount <= 0)
        {
            result.Errors.Add("楼层数量必须大于0");
        }
        
        if (FloorContours.Count != FloorCount)
        {
            result.Warnings.Add($"楼层轮廓数量({FloorContours.Count})与设定楼层数({FloorCount})不匹配");
        }
        
        // 检查楼层编号是否连续
        var floorNumbers = FloorContours.Select(f => f.FloorNumber).OrderBy(n => n).ToList();
        for (int i = 0; i < floorNumbers.Count - 1; i++)
        {
            if (floorNumbers[i + 1] - floorNumbers[i] != 1)
            {
                result.Warnings.Add($"楼层编号不连续: {floorNumbers[i]} -> {floorNumbers[i + 1]}");
            }
        }
        
        // 检查轮廓数据有效性
        foreach (var contour in FloorContours)
        {
            if (contour.OuterContour.Count < 3)
            {
                result.Errors.Add($"第{contour.FloorNumber}层轮廓点数不足（至少需要3个点）");
            }
        }
        
        result.IsValid = result.Errors.Count == 0;
        return result;
    }
    
    /// <summary>
    /// 保存项目到文件
    /// </summary>
    public void SaveToFile(string filePath)
    {
        var directory = Path.GetDirectoryName(filePath);
        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }
        
        LastModified = DateTime.Now;
        var json = JsonConvert.SerializeObject(this, Formatting.Indented);
        File.WriteAllText(filePath, json);
    }
    
    /// <summary>
    /// 从文件加载项目
    /// </summary>
    public static BuildingProject? LoadFromFile(string filePath)
    {
        if (!File.Exists(filePath)) return null;
        
        var json = File.ReadAllText(filePath);
        return JsonConvert.DeserializeObject<BuildingProject>(json);
    }
}

/// <summary>
/// 生成的平面图数据
/// </summary>
public class GeneratedFloorPlan
{
    /// <summary>
    /// 楼层编号
    /// </summary>
    public int FloorNumber { get; set; }
    
    /// <summary>
    /// 生成的图像路径
    /// </summary>
    public string ImagePath { get; set; } = string.Empty;
    
    /// <summary>
    /// 生成时间
    /// </summary>
    public DateTime GeneratedAt { get; set; } = DateTime.Now;
    
    /// <summary>
    /// 生成参数
    /// </summary>
    public GenerationParameters Parameters { get; set; } = new();
    
    /// <summary>
    /// 生成质量评分（0-1）
    /// </summary>
    public float QualityScore { get; set; }
    
    /// <summary>
    /// 用户评分（1-5星）
    /// </summary>
    public int UserRating { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string Notes { get; set; } = string.Empty;
}

/// <summary>
/// 生成参数
/// </summary>
public class GenerationParameters
{
    /// <summary>
    /// 使用的模型名称
    /// </summary>
    public string ModelName { get; set; } = string.Empty;
    
    /// <summary>
    /// 随机种子
    /// </summary>
    public int Seed { get; set; }
    
    /// <summary>
    /// 生成步数
    /// </summary>
    public int Steps { get; set; } = 50;
    
    /// <summary>
    /// 引导强度
    /// </summary>
    public float GuidanceScale { get; set; } = 7.5f;
    
    /// <summary>
    /// 其他参数
    /// </summary>
    public Dictionary<string, object> AdditionalParameters { get; set; } = new();
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }
    
    /// <summary>
    /// 错误信息
    /// </summary>
    public List<string> Errors { get; set; } = new();
    
    /// <summary>
    /// 警告信息
    /// </summary>
    public List<string> Warnings { get; set; } = new();
}
