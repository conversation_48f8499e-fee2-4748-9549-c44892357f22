using Microsoft.Win32;
using System.Windows;

namespace FloorPlanGAN.Studio.Services;

/// <summary>
/// 对话框服务接口
/// </summary>
public interface IDialogService
{
    /// <summary>
    /// 显示信息对话框
    /// </summary>
    Task ShowInfoAsync(string title, string message);
    
    /// <summary>
    /// 显示警告对话框
    /// </summary>
    Task ShowWarningAsync(string title, string message);
    
    /// <summary>
    /// 显示错误对话框
    /// </summary>
    Task ShowErrorAsync(string title, string message);
    
    /// <summary>
    /// 显示确认对话框
    /// </summary>
    Task<bool> ShowConfirmAsync(string title, string message);
    
    /// <summary>
    /// 显示打开文件对话框
    /// </summary>
    Task<string?> ShowOpenFileDialogAsync(string title, string filter, string? initialDirectory = null);
    
    /// <summary>
    /// 显示保存文件对话框
    /// </summary>
    Task<string?> ShowSaveFileDialogAsync(string title, string filter, string? defaultFileName = null);
    
    /// <summary>
    /// 显示文件夹选择对话框
    /// </summary>
    Task<string?> ShowFolderDialogAsync(string title, string? initialDirectory = null);
}

/// <summary>
/// 对话框服务实现
/// </summary>
public class DialogService : IDialogService
{
    public async Task ShowInfoAsync(string title, string message)
    {
        await Application.Current.Dispatcher.InvokeAsync(() =>
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
        });
    }
    
    public async Task ShowWarningAsync(string title, string message)
    {
        await Application.Current.Dispatcher.InvokeAsync(() =>
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
        });
    }
    
    public async Task ShowErrorAsync(string title, string message)
    {
        await Application.Current.Dispatcher.InvokeAsync(() =>
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
        });
    }
    
    public async Task<bool> ShowConfirmAsync(string title, string message)
    {
        return await Application.Current.Dispatcher.InvokeAsync(() =>
        {
            var result = MessageBox.Show(message, title, MessageBoxButton.YesNo, MessageBoxImage.Question);
            return result == MessageBoxResult.Yes;
        });
    }
    
    public async Task<string?> ShowOpenFileDialogAsync(string title, string filter, string? initialDirectory = null)
    {
        return await Application.Current.Dispatcher.InvokeAsync(() =>
        {
            var dialog = new OpenFileDialog
            {
                Title = title,
                Filter = filter,
                InitialDirectory = initialDirectory ?? Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments)
            };
            
            return dialog.ShowDialog() == true ? dialog.FileName : null;
        });
    }
    
    public async Task<string?> ShowSaveFileDialogAsync(string title, string filter, string? defaultFileName = null)
    {
        return await Application.Current.Dispatcher.InvokeAsync(() =>
        {
            var dialog = new SaveFileDialog
            {
                Title = title,
                Filter = filter,
                FileName = defaultFileName ?? "",
                InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments)
            };
            
            return dialog.ShowDialog() == true ? dialog.FileName : null;
        });
    }
    
    public async Task<string?> ShowFolderDialogAsync(string title, string? initialDirectory = null)
    {
        return await Application.Current.Dispatcher.InvokeAsync(() =>
        {
            var dialog = new System.Windows.Forms.FolderBrowserDialog
            {
                Description = title,
                SelectedPath = initialDirectory ?? Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                ShowNewFolderButton = true
            };
            
            return dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK ? dialog.SelectedPath : null;
        });
    }
}
