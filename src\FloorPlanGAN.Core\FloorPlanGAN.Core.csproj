<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <Title>FloorPlanGAN.Core</Title>
    <Description>FloorPlanGAN核心功能库，提供设备管理、工具函数等基础功能</Description>
    <Authors>FloorPlanGAN Team</Authors>
    <Version>1.0.0</Version>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="TorchSharp" Version="0.103.1" />
    <PackageReference Include="TorchSharp.PyBridge" Version="1.4.3" />
    <PackageReference Include="TorchVision" Version="0.103.1" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.5" />
    <PackageReference Include="SixLabors.ImageSharp.Drawing" Version="2.1.4" />
    <PackageReference Include="SkiaSharp" Version="2.88.8" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\src\Maomi.Torch\Maomi.Torch.csproj" />
  </ItemGroup>

</Project>
