<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <ApplicationIcon>icon.ico</ApplicationIcon>
    <AssemblyTitle>FloorPlanGAN Studio</AssemblyTitle>
    <AssemblyDescription>基于GAN的建筑平面图生成工具</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="ModernWpfUI" Version="0.9.6" />
    <PackageReference Include="SkiaSharp.Views.WPF" Version="2.88.8" />
    <PackageReference Include="Microsoft.Win32.Registry" Version="5.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FloorPlanGAN.Core\FloorPlanGAN.Core.csproj" />
    <ProjectReference Include="..\FloorPlanGAN.Models\FloorPlanGAN.Models.csproj" />
    <ProjectReference Include="..\FloorPlanGAN.Data\FloorPlanGAN.Data.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\**\*" />
  </ItemGroup>

</Project>
