using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Windows;
using FloorPlanGAN.Core;
using FloorPlanGAN.Data.Models;
using FloorPlanGAN.Studio.Services;
using Microsoft.Extensions.Logging;

namespace FloorPlanGAN.Studio.ViewModels;

public partial class MainViewModel : ObservableObject
{
    private readonly IProjectService _projectService;
    private readonly IModelService _modelService;
    private readonly IDialogService _dialogService;
    private readonly ILogger<MainViewModel> _logger;
    
    [ObservableProperty]
    private string _projectName = "新建项目";
    
    [ObservableProperty]
    private int _floorCount = 1;
    
    [ObservableProperty]
    private string _projectDescription = "";
    
    [ObservableProperty]
    private ObservableCollection<FloorContour> _floors = new();
    
    [ObservableProperty]
    private FloorContour? _selectedFloor;
    
    [ObservableProperty]
    private string _statusMessage = "就绪";
    
    [ObservableProperty]
    private string _deviceInfo = "";
    
    [ObservableProperty]
    private string _modelStatus = "未加载";
    
    [ObservableProperty]
    private double _modelLoadProgress = 0;
    
    [ObservableProperty]
    private double _trainingProgress = 0;
    
    [ObservableProperty]
    private string _trainingStatus = "";
    
    [ObservableProperty]
    private int _generationSeed = 42;
    
    [ObservableProperty]
    private int _generationCount = 1;
    
    [ObservableProperty]
    private bool _useRandomSeed = false;
    
    private BuildingProject? _currentProject;
    
    public MainViewModel(
        IProjectService projectService,
        IModelService modelService,
        IDialogService dialogService,
        ILogger<MainViewModel> logger)
    {
        _projectService = projectService;
        _modelService = modelService;
        _dialogService = dialogService;
        _logger = logger;
        
        DeviceInfo = DeviceManager.GetDeviceInfo();
        
        // 初始化项目
        InitializeNewProject();
    }
    
    private void InitializeNewProject()
    {
        _currentProject = new BuildingProject
        {
            ProjectName = ProjectName,
            Description = ProjectDescription,
            FloorCount = FloorCount
        };
        
        Floors.Clear();
        AddDefaultFloor();
    }
    
    private void AddDefaultFloor()
    {
        var floor = new FloorContour
        {
            FloorNumber = 1,
            FloorName = "1层",
            FloorType = FloorType.Residential
        };
        
        Floors.Add(floor);
        SelectedFloor = floor;
    }
    
    [RelayCommand]
    private async Task NewProject()
    {
        if (await ConfirmUnsavedChanges())
        {
            InitializeNewProject();
            StatusMessage = "已创建新项目";
        }
    }
    
    [RelayCommand]
    private async Task OpenProject()
    {
        if (!await ConfirmUnsavedChanges()) return;
        
        var filePath = await _dialogService.ShowOpenFileDialogAsync(
            "打开项目",
            "FloorPlanGAN项目文件 (*.fpg)|*.fpg|所有文件 (*.*)|*.*");
        
        if (!string.IsNullOrEmpty(filePath))
        {
            try
            {
                _currentProject = await _projectService.LoadProjectAsync(filePath);
                if (_currentProject != null)
                {
                    ProjectName = _currentProject.ProjectName;
                    ProjectDescription = _currentProject.Description;
                    FloorCount = _currentProject.FloorCount;
                    
                    Floors.Clear();
                    foreach (var floor in _currentProject.FloorContours)
                    {
                        Floors.Add(floor);
                    }
                    
                    StatusMessage = $"已打开项目: {Path.GetFileName(filePath)}";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开项目失败");
                await _dialogService.ShowErrorAsync("打开项目失败", ex.Message);
            }
        }
    }
    
    [RelayCommand]
    private async Task SaveProject()
    {
        if (_currentProject == null) return;
        
        UpdateCurrentProject();
        
        try
        {
            await _projectService.SaveProjectAsync(_currentProject);
            StatusMessage = "项目已保存";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存项目失败");
            await _dialogService.ShowErrorAsync("保存项目失败", ex.Message);
        }
    }
    
    [RelayCommand]
    private async Task SaveAsProject()
    {
        if (_currentProject == null) return;
        
        var filePath = await _dialogService.ShowSaveFileDialogAsync(
            "另存为项目",
            "FloorPlanGAN项目文件 (*.fpg)|*.fpg",
            $"{ProjectName}.fpg");
        
        if (!string.IsNullOrEmpty(filePath))
        {
            UpdateCurrentProject();
            
            try
            {
                await _projectService.SaveProjectAsAsync(_currentProject, filePath);
                StatusMessage = $"项目已保存为: {Path.GetFileName(filePath)}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "另存项目失败");
                await _dialogService.ShowErrorAsync("另存项目失败", ex.Message);
            }
        }
    }
    
    [RelayCommand]
    private void AddFloor()
    {
        var newFloorNumber = Floors.Count > 0 ? Floors.Max(f => f.FloorNumber) + 1 : 1;
        var floor = new FloorContour
        {
            FloorNumber = newFloorNumber,
            FloorName = $"{newFloorNumber}层",
            FloorType = FloorType.Residential
        };
        
        Floors.Add(floor);
        SelectedFloor = floor;
        FloorCount = Floors.Count;
        
        StatusMessage = $"已添加第{newFloorNumber}层";
    }
    
    [RelayCommand]
    private void RemoveFloor(FloorContour floor)
    {
        if (floor != null && Floors.Contains(floor))
        {
            Floors.Remove(floor);
            FloorCount = Floors.Count;
            
            if (SelectedFloor == floor)
            {
                SelectedFloor = Floors.FirstOrDefault();
            }
            
            StatusMessage = $"已删除第{floor.FloorNumber}层";
        }
    }
    
    [RelayCommand]
    private async Task Generate()
    {
        if (SelectedFloor == null)
        {
            await _dialogService.ShowWarningAsync("提示", "请先选择要生成的楼层");
            return;
        }
        
        if (SelectedFloor.OuterContour.Count < 3)
        {
            await _dialogService.ShowWarningAsync("提示", "请先绘制楼层轮廓（至少3个点）");
            return;
        }
        
        try
        {
            StatusMessage = "正在生成平面图...";
            
            var seed = UseRandomSeed ? null : GenerationSeed;
            var result = await _modelService.GenerateFloorPlanAsync(SelectedFloor, seed);
            
            if (result != null)
            {
                StatusMessage = "平面图生成完成";
                // TODO: 显示生成结果
            }
            else
            {
                StatusMessage = "生成失败";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成平面图失败");
            await _dialogService.ShowErrorAsync("生成失败", ex.Message);
            StatusMessage = "生成失败";
        }
    }
    
    [RelayCommand]
    private async Task LoadModel()
    {
        var filePath = await _dialogService.ShowOpenFileDialogAsync(
            "加载模型",
            "模型文件 (*.dat)|*.dat|所有文件 (*.*)|*.*");
        
        if (!string.IsNullOrEmpty(filePath))
        {
            try
            {
                ModelStatus = "正在加载...";
                ModelLoadProgress = 0;
                
                await _modelService.LoadModelAsync(filePath, progress =>
                {
                    ModelLoadProgress = progress * 100;
                });
                
                ModelStatus = "已加载";
                ModelLoadProgress = 100;
                StatusMessage = "模型加载完成";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载模型失败");
                await _dialogService.ShowErrorAsync("加载模型失败", ex.Message);
                ModelStatus = "加载失败";
                ModelLoadProgress = 0;
            }
        }
    }
    
    [RelayCommand]
    private async Task StartTraining()
    {
        // TODO: 实现训练逻辑
        await _dialogService.ShowInfoAsync("提示", "训练功能正在开发中...");
    }
    
    [RelayCommand]
    private void Exit()
    {
        Application.Current.Shutdown();
    }
    
    private void UpdateCurrentProject()
    {
        if (_currentProject == null) return;
        
        _currentProject.ProjectName = ProjectName;
        _currentProject.Description = ProjectDescription;
        _currentProject.FloorCount = FloorCount;
        _currentProject.FloorContours = Floors.ToList();
        _currentProject.LastModified = DateTime.Now;
    }
    
    private async Task<bool> ConfirmUnsavedChanges()
    {
        // TODO: 检查是否有未保存的更改
        return true;
    }
}
