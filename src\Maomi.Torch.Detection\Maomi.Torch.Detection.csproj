﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>Library</OutputType>
		<TargetFrameworks>net6.0;net7.0;net8.0;net9.0</TargetFrameworks>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<RootNamespace>Maomi.Torch</RootNamespace>

		<IsPackable>true</IsPackable>
		<PackageVersion>0.0.5</PackageVersion>
		<Title>Maomi.Torch.Detection</Title>
		<Description>
			C# Pytorch 深度学习框架扩展包.
		</Description>
		<GeneratePackageOnBuild>True</GeneratePackageOnBuild>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<PackageLicenseExpression>MIT</PackageLicenseExpression>
		<PackageIcon>package.png</PackageIcon>
		<PackageProjectUrl>https://torch.whuanle.cn</PackageProjectUrl>
		<RepositoryUrl>https://github.com/whuanle/cs_pytorch</RepositoryUrl>
		<PackageReadmeFile>README.md</PackageReadmeFile>
		<RepositoryType>git</RepositoryType>
		<PackageReleaseNotes>
			C# Pytorch 深度学习框架扩展包.
		</PackageReleaseNotes>
		<IncludeSymbols>True</IncludeSymbols>
		<SymbolPackageFormat>snupkg</SymbolPackageFormat>
		<PackageRequireLicenseAcceptance>True</PackageRequireLicenseAcceptance>
	</PropertyGroup>

	<ItemGroup>
		<None Include="package.png" />
	</ItemGroup>

	<ItemGroup>
		<None Include="..\..\README.md">
			<Pack>True</Pack>
			<PackagePath>\</PackagePath>
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\Maomi.Torch\Maomi.Torch.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Models\" />
	</ItemGroup>

</Project>
