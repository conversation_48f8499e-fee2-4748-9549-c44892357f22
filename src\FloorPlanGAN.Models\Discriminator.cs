using TorchSharp;
using TorchSharp.Modules;
using static TorchSharp.torch;
using static TorchSharp.torch.nn;

namespace FloorPlanGAN.Models;

/// <summary>
/// 条件GAN判别器 - 判断平面图的真实性和与轮廓的匹配度
/// </summary>
public class FloorPlanDiscriminator : Module<Tensor, Tensor, Tensor>
{
    private readonly int _features;
    private readonly int _imageSize;
    
    // 平面图编码器
    private readonly Sequential _imageEncoder;
    
    // 轮廓编码器
    private readonly Sequential _contourEncoder;
    
    // 融合和判别层
    private readonly Sequential _discriminator;
    
    public FloorPlanDiscriminator(int imageSize = 512, int features = 64) 
        : base(nameof(FloorPlanDiscriminator))
    {
        _imageSize = imageSize;
        _features = features;
        
        // 平面图编码器
        _imageEncoder = Sequential(
            // 输入: [batch, 1, 512, 512] (平面图)
            Conv2d(1, features, kernelSize: 4, stride: 2, padding: 1), // [batch, 64, 256, 256]
            LeakyReLU(0.2f),
            
            Conv2d(features, features * 2, kernelSize: 4, stride: 2, padding: 1), // [batch, 128, 128, 128]
            BatchNorm2d(features * 2),
            LeakyReLU(0.2f),
            
            Conv2d(features * 2, features * 4, kernelSize: 4, stride: 2, padding: 1), // [batch, 256, 64, 64]
            BatchNorm2d(features * 4),
            LeakyReLU(0.2f),
            
            Conv2d(features * 4, features * 8, kernelSize: 4, stride: 2, padding: 1), // [batch, 512, 32, 32]
            BatchNorm2d(features * 8),
            LeakyReLU(0.2f),
            
            Conv2d(features * 8, features * 8, kernelSize: 4, stride: 2, padding: 1), // [batch, 512, 16, 16]
            BatchNorm2d(features * 8),
            LeakyReLU(0.2f),
            
            Conv2d(features * 8, features * 8, kernelSize: 4, stride: 2, padding: 1), // [batch, 512, 8, 8]
            BatchNorm2d(features * 8),
            LeakyReLU(0.2f),
            
            Conv2d(features * 8, features * 8, kernelSize: 4, stride: 2, padding: 1), // [batch, 512, 4, 4]
            BatchNorm2d(features * 8),
            LeakyReLU(0.2f)
        );
        
        // 轮廓编码器（与生成器中的条件编码器类似但独立）
        _contourEncoder = Sequential(
            // 输入: [batch, 1, 512, 512] (轮廓图像)
            Conv2d(1, features, kernelSize: 4, stride: 2, padding: 1), // [batch, 64, 256, 256]
            LeakyReLU(0.2f),
            
            Conv2d(features, features * 2, kernelSize: 4, stride: 2, padding: 1), // [batch, 128, 128, 128]
            BatchNorm2d(features * 2),
            LeakyReLU(0.2f),
            
            Conv2d(features * 2, features * 4, kernelSize: 4, stride: 2, padding: 1), // [batch, 256, 64, 64]
            BatchNorm2d(features * 4),
            LeakyReLU(0.2f),
            
            Conv2d(features * 4, features * 8, kernelSize: 4, stride: 2, padding: 1), // [batch, 512, 32, 32]
            BatchNorm2d(features * 8),
            LeakyReLU(0.2f),
            
            Conv2d(features * 8, features * 8, kernelSize: 4, stride: 2, padding: 1), // [batch, 512, 16, 16]
            BatchNorm2d(features * 8),
            LeakyReLU(0.2f),
            
            Conv2d(features * 8, features * 8, kernelSize: 4, stride: 2, padding: 1), // [batch, 512, 8, 8]
            BatchNorm2d(features * 8),
            LeakyReLU(0.2f),
            
            Conv2d(features * 8, features * 8, kernelSize: 4, stride: 2, padding: 1), // [batch, 512, 4, 4]
            BatchNorm2d(features * 8),
            LeakyReLU(0.2f)
        );
        
        // 融合和判别层
        _discriminator = Sequential(
            // 输入: [batch, 1024, 4, 4] (平面图特征 + 轮廓特征)
            Conv2d(features * 8 * 2, features * 16, kernelSize: 4, stride: 1, padding: 0), // [batch, 1024, 1, 1]
            Flatten(), // [batch, 1024]
            
            Linear(features * 16, features * 4), // [batch, 256]
            LeakyReLU(0.2f),
            Dropout(0.5),
            
            Linear(features * 4, features), // [batch, 64]
            LeakyReLU(0.2f),
            Dropout(0.5),
            
            Linear(features, 1), // [batch, 1]
            Sigmoid() // 输出概率 [0, 1]
        );
        
        RegisterComponents();
    }
    
    public override Tensor forward(Tensor floorPlan, Tensor contour)
    {
        // 编码平面图
        var imageFeatures = _imageEncoder.forward(floorPlan); // [batch, 512, 4, 4]
        
        // 编码轮廓
        var contourFeatures = _contourEncoder.forward(contour); // [batch, 512, 4, 4]
        
        // 融合特征
        var combined = torch.cat(new[] { imageFeatures, contourFeatures }, dim: 1); // [batch, 1024, 4, 4]
        
        // 判别
        var output = _discriminator.forward(combined); // [batch, 1]
        
        return output;
    }
    
    /// <summary>
    /// 计算真实样本的损失
    /// </summary>
    public Tensor CalculateRealLoss(Tensor realFloorPlans, Tensor contours)
    {
        var predictions = forward(realFloorPlans, contours);
        var realLabels = torch.ones_like(predictions);
        return functional.binary_cross_entropy(predictions, realLabels);
    }
    
    /// <summary>
    /// 计算生成样本的损失
    /// </summary>
    public Tensor CalculateFakeLoss(Tensor fakeFloorPlans, Tensor contours)
    {
        var predictions = forward(fakeFloorPlans, contours);
        var fakeLabels = torch.zeros_like(predictions);
        return functional.binary_cross_entropy(predictions, fakeLabels);
    }
    
    /// <summary>
    /// 计算总判别器损失
    /// </summary>
    public Tensor CalculateLoss(Tensor realFloorPlans, Tensor fakeFloorPlans, Tensor contours)
    {
        var realLoss = CalculateRealLoss(realFloorPlans, contours);
        var fakeLoss = CalculateFakeLoss(fakeFloorPlans.detach(), contours);
        return (realLoss + fakeLoss) / 2;
    }
    
    /// <summary>
    /// 评估平面图质量
    /// </summary>
    public float EvaluateQuality(Tensor floorPlan, Tensor contour)
    {
        using (torch.no_grad())
        {
            var prediction = forward(floorPlan, contour);
            return prediction.item<float>();
        }
    }
    
    /// <summary>
    /// 批量评估质量
    /// </summary>
    public float[] EvaluateQualityBatch(Tensor floorPlans, Tensor contours)
    {
        using (torch.no_grad())
        {
            var predictions = forward(floorPlans, contours);
            return predictions.squeeze().data<float>().ToArray();
        }
    }
}
