using TorchSharp;
using FloorPlanGAN.Data.Models;
using FloorPlanGAN.Models;

namespace FloorPlanGAN.Studio.Services;

/// <summary>
/// 模型服务接口
/// </summary>
public interface IModelService
{
    /// <summary>
    /// 是否已加载模型
    /// </summary>
    bool IsModelLoaded { get; }
    
    /// <summary>
    /// 加载模型
    /// </summary>
    Task LoadModelAsync(string modelPath, Action<float>? progressCallback = null);
    
    /// <summary>
    /// 卸载模型
    /// </summary>
    void UnloadModel();
    
    /// <summary>
    /// 生成平面图
    /// </summary>
    Task<GeneratedFloorPlan?> GenerateFloorPlanAsync(FloorContour contour, int? seed = null);
    
    /// <summary>
    /// 批量生成平面图变体
    /// </summary>
    Task<List<GeneratedFloorPlan>> GenerateVariantsAsync(FloorContour contour, int variantCount = 5, int? baseSeed = null);
    
    /// <summary>
    /// 评估生成质量
    /// </summary>
    Task<float> EvaluateQualityAsync(GeneratedFloorPlan plan, FloorContour contour);
    
    /// <summary>
    /// 获取模型信息
    /// </summary>
    ModelInfo GetModelInfo();
}

/// <summary>
/// 模型服务实现
/// </summary>
public class ModelService : IModelService, IDisposable
{
    private FloorPlanGenerator? _generator;
    private FloorPlanDiscriminator? _discriminator;
    private readonly IImageProcessingService _imageProcessingService;
    private bool _disposed = false;
    
    public bool IsModelLoaded => _generator != null;
    
    public ModelService(IImageProcessingService imageProcessingService)
    {
        _imageProcessingService = imageProcessingService;
    }
    
    public async Task LoadModelAsync(string modelPath, Action<float>? progressCallback = null)
    {
        await Task.Run(() =>
        {
            progressCallback?.Invoke(0.1f);
            
            // 检查模型文件是否存在
            if (!Directory.Exists(modelPath))
            {
                throw new DirectoryNotFoundException($"模型目录不存在: {modelPath}");
            }
            
            var generatorPath = Path.Combine(modelPath, "generator.dat");
            var discriminatorPath = Path.Combine(modelPath, "discriminator.dat");
            
            if (!File.Exists(generatorPath))
            {
                throw new FileNotFoundException($"生成器模型文件不存在: {generatorPath}");
            }
            
            progressCallback?.Invoke(0.3f);
            
            // 创建模型实例
            _generator = new FloorPlanGenerator();
            _generator.load(generatorPath);
            _generator.eval(); // 设置为评估模式
            
            progressCallback?.Invoke(0.7f);
            
            // 加载判别器（可选，用于质量评估）
            if (File.Exists(discriminatorPath))
            {
                _discriminator = new FloorPlanDiscriminator();
                _discriminator.load(discriminatorPath);
                _discriminator.eval();
            }
            
            progressCallback?.Invoke(1.0f);
        });
    }
    
    public void UnloadModel()
    {
        _generator?.Dispose();
        _generator = null;
        
        _discriminator?.Dispose();
        _discriminator = null;
    }
    
    public async Task<GeneratedFloorPlan?> GenerateFloorPlanAsync(FloorContour contour, int? seed = null)
    {
        if (!IsModelLoaded)
            throw new InvalidOperationException("模型未加载");
        
        return await Task.Run(() =>
        {
            try
            {
                // 将轮廓转换为张量
                var contourTensor = _imageProcessingService.ContourToTensor(contour);
                
                // 生成平面图
                var generatedTensor = _generator!.GenerateFromContour(contourTensor, seed);
                
                // 保存生成的图像
                var outputDir = Path.Combine("output", "generated", DateTime.Now.ToString("yyyyMMdd"));
                Directory.CreateDirectory(outputDir);
                
                var fileName = $"floor_{contour.FloorNumber}_{DateTime.Now:HHmmss}.png";
                var imagePath = Path.Combine(outputDir, fileName);
                
                _imageProcessingService.SaveTensorAsImage(generatedTensor, imagePath);
                
                // 评估质量
                float qualityScore = 0.5f; // 默认分数
                if (_discriminator != null)
                {
                    qualityScore = _discriminator.EvaluateQuality(generatedTensor, contourTensor);
                }
                
                return new GeneratedFloorPlan
                {
                    FloorNumber = contour.FloorNumber,
                    ImagePath = imagePath,
                    GeneratedAt = DateTime.Now,
                    QualityScore = qualityScore,
                    Parameters = new GenerationParameters
                    {
                        ModelName = "FloorPlanGAN",
                        Seed = seed ?? 0,
                        Steps = 50
                    }
                };
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"生成平面图失败: {ex.Message}", ex);
            }
        });
    }
    
    public async Task<List<GeneratedFloorPlan>> GenerateVariantsAsync(FloorContour contour, int variantCount = 5, int? baseSeed = null)
    {
        if (!IsModelLoaded)
            throw new InvalidOperationException("模型未加载");
        
        var variants = new List<GeneratedFloorPlan>();
        
        for (int i = 0; i < variantCount; i++)
        {
            var seed = baseSeed.HasValue ? baseSeed.Value + i : null;
            var variant = await GenerateFloorPlanAsync(contour, seed);
            if (variant != null)
            {
                variants.Add(variant);
            }
        }
        
        return variants;
    }
    
    public async Task<float> EvaluateQualityAsync(GeneratedFloorPlan plan, FloorContour contour)
    {
        if (_discriminator == null)
            return plan.QualityScore; // 返回已有的分数
        
        return await Task.Run(() =>
        {
            try
            {
                var contourTensor = _imageProcessingService.ContourToTensor(contour);
                var planTensor = _imageProcessingService.LoadImageAsTensor(plan.ImagePath);
                
                return _discriminator.EvaluateQuality(planTensor, contourTensor);
            }
            catch
            {
                return plan.QualityScore; // 出错时返回原分数
            }
        });
    }
    
    public ModelInfo GetModelInfo()
    {
        return new ModelInfo
        {
            IsLoaded = IsModelLoaded,
            HasGenerator = _generator != null,
            HasDiscriminator = _discriminator != null,
            ModelType = "FloorPlanGAN",
            Version = "1.0.0"
        };
    }
    
    public void Dispose()
    {
        if (!_disposed)
        {
            UnloadModel();
            _disposed = true;
        }
    }
}

/// <summary>
/// 模型信息
/// </summary>
public class ModelInfo
{
    public bool IsLoaded { get; set; }
    public bool HasGenerator { get; set; }
    public bool HasDiscriminator { get; set; }
    public string ModelType { get; set; } = "";
    public string Version { get; set; } = "";
}
