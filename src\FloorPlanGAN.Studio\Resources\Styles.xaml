<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.modernwpf.com/2019">

    <!-- 标题样式 -->
    <Style x:Key="TitleTextBlockStyle" TargetType="TextBlock" BasedOn="{StaticResource {x:Type TextBlock}}">
        <Setter Property="FontSize" Value="24" />
        <Setter Property="FontWeight" Value="Bold" />
        <Setter Property="Margin" Value="0,0,0,10" />
    </Style>

    <!-- 副标题样式 -->
    <Style x:Key="SubtitleTextBlockStyle" TargetType="TextBlock" BasedOn="{StaticResource {x:Type TextBlock}}">
        <Setter Property="FontSize" Value="16" />
        <Setter Property="FontWeight" Value="SemiBold" />
        <Setter Property="Margin" Value="0,0,0,5" />
    </Style>

    <!-- 卡片样式 -->
    <Style x:Key="CardBorderStyle" TargetType="Border">
        <Setter Property="Background" Value="{DynamicResource SystemControlBackgroundChromeMediumLowBrush}" />
        <Setter Property="BorderBrush" Value="{DynamicResource SystemControlForegroundBaseMediumLowBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="CornerRadius" Value="4" />
        <Setter Property="Padding" Value="12" />
        <Setter Property="Margin" Value="4" />
    </Style>

    <!-- 工具栏按钮样式 -->
    <Style x:Key="ToolbarButtonStyle" TargetType="Button" BasedOn="{StaticResource {x:Type Button}}">
        <Setter Property="Padding" Value="8,4" />
        <Setter Property="Margin" Value="2" />
        <Setter Property="MinWidth" Value="80" />
    </Style>

    <!-- 图标按钮样式 -->
    <Style x:Key="IconButtonStyle" TargetType="Button" BasedOn="{StaticResource {x:Type Button}}">
        <Setter Property="Width" Value="32" />
        <Setter Property="Height" Value="32" />
        <Setter Property="Padding" Value="4" />
        <Setter Property="Margin" Value="2" />
    </Style>

    <!-- 面板标题样式 -->
    <Style x:Key="PanelHeaderStyle" TargetType="TextBlock" BasedOn="{StaticResource SubtitleTextBlockStyle}">
        <Setter Property="Background" Value="{DynamicResource SystemControlBackgroundAccentBrush}" />
        <Setter Property="Foreground" Value="{DynamicResource SystemControlForegroundChromeWhiteBrush}" />
        <Setter Property="Padding" Value="8,4" />
        <Setter Property="Margin" Value="0,0,0,8" />
    </Style>

    <!-- 状态指示器样式 -->
    <Style x:Key="StatusIndicatorStyle" TargetType="Ellipse">
        <Setter Property="Width" Value="12" />
        <Setter Property="Height" Value="12" />
        <Setter Property="Margin" Value="4,0" />
    </Style>

    <!-- 成功状态 -->
    <Style x:Key="SuccessStatusStyle" TargetType="Ellipse" BasedOn="{StaticResource StatusIndicatorStyle}">
        <Setter Property="Fill" Value="Green" />
    </Style>

    <!-- 警告状态 -->
    <Style x:Key="WarningStatusStyle" TargetType="Ellipse" BasedOn="{StaticResource StatusIndicatorStyle}">
        <Setter Property="Fill" Value="Orange" />
    </Style>

    <!-- 错误状态 -->
    <Style x:Key="ErrorStatusStyle" TargetType="Ellipse" BasedOn="{StaticResource StatusIndicatorStyle}">
        <Setter Property="Fill" Value="Red" />
    </Style>

    <!-- 输入组样式 -->
    <Style x:Key="InputGroupStyle" TargetType="StackPanel">
        <Setter Property="Margin" Value="0,0,0,12" />
    </Style>

    <!-- 标签样式 -->
    <Style x:Key="LabelStyle" TargetType="TextBlock" BasedOn="{StaticResource {x:Type TextBlock}}">
        <Setter Property="Margin" Value="0,0,0,4" />
        <Setter Property="FontWeight" Value="Medium" />
    </Style>

    <!-- 分组框样式 -->
    <Style x:Key="GroupBoxStyle" TargetType="GroupBox" BasedOn="{StaticResource {x:Type GroupBox}}">
        <Setter Property="Margin" Value="0,0,0,12" />
        <Setter Property="Padding" Value="8" />
    </Style>

    <!-- 进度条样式 -->
    <Style x:Key="ModernProgressBarStyle" TargetType="ProgressBar" BasedOn="{StaticResource {x:Type ProgressBar}}">
        <Setter Property="Height" Value="6" />
        <Setter Property="Margin" Value="0,4" />
    </Style>

    <!-- 列表项样式 */
    <Style x:Key="ListItemStyle" TargetType="ListViewItem" BasedOn="{StaticResource {x:Type ListViewItem}}">
        <Setter Property="Padding" Value="8" />
        <Setter Property="Margin" Value="0,1" />
    </Style>

    <!-- 工具提示样式 -->
    <Style x:Key="ModernToolTipStyle" TargetType="ToolTip">
        <Setter Property="Background" Value="{DynamicResource SystemControlBackgroundChromeMediumBrush}" />
        <Setter Property="BorderBrush" Value="{DynamicResource SystemControlForegroundBaseMediumBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Padding" Value="8,4" />
        <Setter Property="FontSize" Value="12" />
    </Style>

</ResourceDictionary>
