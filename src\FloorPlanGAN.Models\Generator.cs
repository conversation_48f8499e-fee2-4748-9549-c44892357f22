using TorchSharp;
using TorchSharp.Modules;
using static TorchSharp.torch;
using static TorchSharp.torch.nn;

namespace FloorPlanGAN.Models;

/// <summary>
/// 条件GAN生成器 - 基于楼层轮廓生成室内布局
/// </summary>
public class FloorPlanGenerator : Module<Tensor, Tensor, Tensor>
{
    private readonly int _noiseSize;
    private readonly int _conditionSize;
    private readonly int _imageSize;
    private readonly int _features;
    
    // 条件编码器 - 将轮廓图像编码为特征向量
    private readonly Sequential _conditionEncoder;
    
    // 噪声和条件融合层
    private readonly Linear _fusionLayer;
    
    // 生成器主体
    private readonly Sequential _generator;
    
    public FloorPlanGenerator(int noiseSize = 100, int conditionSize = 256, int imageSize = 512, int features = 64) 
        : base(nameof(FloorPlanGenerator))
    {
        _noiseSize = noiseSize;
        _conditionSize = conditionSize;
        _imageSize = imageSize;
        _features = features;
        
        // 条件编码器 - 将轮廓图像编码为特征向量
        _conditionEncoder = Sequential(
            // 输入: [batch, 1, 512, 512] (轮廓图像)
            Conv2d(1, features, kernelSize: 4, stride: 2, padding: 1), // [batch, 64, 256, 256]
            LeakyReLU(0.2f),
            
            Conv2d(features, features * 2, kernelSize: 4, stride: 2, padding: 1), // [batch, 128, 128, 128]
            BatchNorm2d(features * 2),
            LeakyReLU(0.2f),
            
            Conv2d(features * 2, features * 4, kernelSize: 4, stride: 2, padding: 1), // [batch, 256, 64, 64]
            BatchNorm2d(features * 4),
            LeakyReLU(0.2f),
            
            Conv2d(features * 4, features * 8, kernelSize: 4, stride: 2, padding: 1), // [batch, 512, 32, 32]
            BatchNorm2d(features * 8),
            LeakyReLU(0.2f),
            
            Conv2d(features * 8, features * 8, kernelSize: 4, stride: 2, padding: 1), // [batch, 512, 16, 16]
            BatchNorm2d(features * 8),
            LeakyReLU(0.2f),
            
            Conv2d(features * 8, features * 8, kernelSize: 4, stride: 2, padding: 1), // [batch, 512, 8, 8]
            BatchNorm2d(features * 8),
            LeakyReLU(0.2f),
            
            // 全局平均池化
            AdaptiveAvgPool2d(1), // [batch, 512, 1, 1]
            Flatten(), // [batch, 512]
            Linear(features * 8, conditionSize), // [batch, 256]
            ReLU()
        );
        
        // 噪声和条件融合
        _fusionLayer = Linear(noiseSize + conditionSize, features * 8 * 4 * 4);
        
        // 生成器主体 - 上采样生成图像
        _generator = Sequential(
            // 输入: [batch, 512*4*4] -> reshape to [batch, 512, 4, 4]
            ReLU(),
            
            // 第一层上采样: 4x4 -> 8x8
            ConvTranspose2d(features * 8, features * 8, kernelSize: 4, stride: 2, padding: 1),
            BatchNorm2d(features * 8),
            ReLU(),
            
            // 第二层上采样: 8x8 -> 16x16
            ConvTranspose2d(features * 8, features * 8, kernelSize: 4, stride: 2, padding: 1),
            BatchNorm2d(features * 8),
            ReLU(),
            
            // 第三层上采样: 16x16 -> 32x32
            ConvTranspose2d(features * 8, features * 4, kernelSize: 4, stride: 2, padding: 1),
            BatchNorm2d(features * 4),
            ReLU(),
            
            // 第四层上采样: 32x32 -> 64x64
            ConvTranspose2d(features * 4, features * 2, kernelSize: 4, stride: 2, padding: 1),
            BatchNorm2d(features * 2),
            ReLU(),
            
            // 第五层上采样: 64x64 -> 128x128
            ConvTranspose2d(features * 2, features, kernelSize: 4, stride: 2, padding: 1),
            BatchNorm2d(features),
            ReLU(),
            
            // 第六层上采样: 128x128 -> 256x256
            ConvTranspose2d(features, features, kernelSize: 4, stride: 2, padding: 1),
            BatchNorm2d(features),
            ReLU(),
            
            // 第七层上采样: 256x256 -> 512x512
            ConvTranspose2d(features, 1, kernelSize: 4, stride: 2, padding: 1),
            Tanh() // 输出范围 [-1, 1]
        );
        
        RegisterComponents();
    }
    
    public override Tensor forward(Tensor noise, Tensor condition)
    {
        // 编码条件（轮廓图像）
        var conditionFeatures = _conditionEncoder.forward(condition); // [batch, 256]
        
        // 融合噪声和条件
        var combined = torch.cat(new[] { noise, conditionFeatures }, dim: 1); // [batch, 100+256]
        var fused = _fusionLayer.forward(combined); // [batch, 512*4*4]
        
        // 重塑为4D张量
        var batchSize = fused.shape[0];
        var reshaped = fused.view(batchSize, _features * 8, 4, 4); // [batch, 512, 4, 4]
        
        // 生成图像
        var generated = _generator.forward(reshaped); // [batch, 1, 512, 512]
        
        return generated;
    }
    
    /// <summary>
    /// 生成随机噪声
    /// </summary>
    public Tensor GenerateNoise(int batchSize, Device? device = null)
    {
        device ??= torch.device("cpu");
        return torch.randn(batchSize, _noiseSize, device: device);
    }
    
    /// <summary>
    /// 从轮廓生成平面图
    /// </summary>
    public Tensor GenerateFromContour(Tensor contourImage, int? seed = null)
    {
        if (seed.HasValue)
        {
            torch.manual_seed(seed.Value);
        }
        
        var batchSize = contourImage.shape[0];
        var device = contourImage.device;
        
        using (torch.no_grad())
        {
            var noise = GenerateNoise(batchSize, device);
            return forward(noise, contourImage);
        }
    }
    
    /// <summary>
    /// 批量生成多个变体
    /// </summary>
    public List<Tensor> GenerateVariants(Tensor contourImage, int variantCount = 5, int? baseSeed = null)
    {
        var variants = new List<Tensor>();
        var device = contourImage.device;
        
        using (torch.no_grad())
        {
            for (int i = 0; i < variantCount; i++)
            {
                var seed = baseSeed.HasValue ? baseSeed.Value + i : null;
                if (seed.HasValue)
                {
                    torch.manual_seed(seed.Value);
                }
                
                var noise = GenerateNoise(1, device);
                var generated = forward(noise, contourImage);
                variants.Add(generated.clone());
            }
        }
        
        return variants;
    }
}
