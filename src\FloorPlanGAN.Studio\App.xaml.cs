using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Windows;
using FloorPlanGAN.Core;
using FloorPlanGAN.Core.Configuration;
using FloorPlanGAN.Studio.ViewModels;
using FloorPlanGAN.Studio.Services;
using FloorPlanGAN.Studio.Views;

namespace FloorPlanGAN.Studio;

/// <summary>
/// App.xaml 的交互逻辑
/// </summary>
public partial class App : Application
{
    private IHost? _host;
    
    protected override async void OnStartup(StartupEventArgs e)
    {
        // 初始化设备管理器
        DeviceManager.Initialize();
        
        // 构建依赖注入容器
        _host = Host.CreateDefaultBuilder()
            .ConfigureServices(ConfigureServices)
            .ConfigureLogging(logging =>
            {
                logging.AddConsole();
                logging.SetMinimumLevel(LogLevel.Information);
            })
            .Build();
        
        await _host.StartAsync();
        
        // 显示主窗口
        var mainWindow = _host.Services.GetRequiredService<MainWindow>();
        mainWindow.Show();
        
        base.OnStartup(e);
    }
    
    protected override async void OnExit(ExitEventArgs e)
    {
        if (_host != null)
        {
            await _host.StopAsync();
            _host.Dispose();
        }
        
        base.OnExit(e);
    }
    
    private void ConfigureServices(IServiceCollection services)
    {
        // 配置
        var config = AppConfig.LoadFromFile("config.json");
        services.AddSingleton(config);
        services.AddSingleton(config.Training);
        services.AddSingleton(config.UI);
        services.AddSingleton(config.Data);
        
        // 服务
        services.AddSingleton<IProjectService, ProjectService>();
        services.AddSingleton<IImageProcessingService, ImageProcessingService>();
        services.AddSingleton<IModelService, ModelService>();
        services.AddSingleton<IDialogService, DialogService>();
        
        // ViewModels
        services.AddTransient<MainViewModel>();
        services.AddTransient<ProjectViewModel>();
        services.AddTransient<ContourEditorViewModel>();
        services.AddTransient<GenerationViewModel>();
        services.AddTransient<TrainingViewModel>();
        
        // Views
        services.AddTransient<MainWindow>();
    }
}
