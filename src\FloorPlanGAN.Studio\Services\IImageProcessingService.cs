using TorchSharp;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Drawing.Processing;
using SixLabors.ImageSharp.Processing;
using FloorPlanGAN.Data.Models;
using FloorPlanGAN.Core;

namespace FloorPlanGAN.Studio.Services;

/// <summary>
/// 图像处理服务接口
/// </summary>
public interface IImageProcessingService
{
    /// <summary>
    /// 将轮廓转换为张量
    /// </summary>
    torch.Tensor ContourToTensor(FloorContour contour, int imageSize = 512);
    
    /// <summary>
    /// 将张量保存为图像
    /// </summary>
    void SaveTensorAsImage(torch.Tensor tensor, string filePath);
    
    /// <summary>
    /// 加载图像为张量
    /// </summary>
    torch.Tensor LoadImageAsTensor(string imagePath, int imageSize = 512);
    
    /// <summary>
    /// 将轮廓渲染为图像
    /// </summary>
    Image<Rgba32> RenderContourToImage(FloorContour contour, int imageSize = 512);
    
    /// <summary>
    /// 图像预处理
    /// </summary>
    torch.Tensor PreprocessImage(Image<Rgba32> image);
    
    /// <summary>
    /// 图像后处理
    /// </summary>
    Image<Rgba32> PostprocessTensor(torch.Tensor tensor);
}

/// <summary>
/// 图像处理服务实现
/// </summary>
public class ImageProcessingService : IImageProcessingService
{
    public torch.Tensor ContourToTensor(FloorContour contour, int imageSize = 512)
    {
        // 渲染轮廓为图像
        var image = RenderContourToImage(contour, imageSize);
        
        // 转换为张量
        return PreprocessImage(image);
    }
    
    public void SaveTensorAsImage(torch.Tensor tensor, string filePath)
    {
        // 确保目录存在
        var directory = Path.GetDirectoryName(filePath);
        if (!string.IsNullOrEmpty(directory))
        {
            Directory.CreateDirectory(directory);
        }
        
        // 后处理张量为图像
        var image = PostprocessTensor(tensor);
        
        // 保存图像
        image.Save(filePath);
        image.Dispose();
    }
    
    public torch.Tensor LoadImageAsTensor(string imagePath, int imageSize = 512)
    {
        if (!File.Exists(imagePath))
            throw new FileNotFoundException($"图像文件不存在: {imagePath}");
        
        using var image = Image.Load<Rgba32>(imagePath);
        
        // 调整大小
        image.Mutate(x => x.Resize(imageSize, imageSize));
        
        return PreprocessImage(image);
    }
    
    public Image<Rgba32> RenderContourToImage(FloorContour contour, int imageSize = 512)
    {
        var image = new Image<Rgba32>(imageSize, imageSize, Color.Black);
        
        if (contour.OuterContour.Count < 3)
            return image;
        
        // 标准化轮廓到图像尺寸
        var normalizedContour = contour.Normalize(imageSize);
        
        image.Mutate(ctx =>
        {
            // 绘制外轮廓
            var points = normalizedContour.OuterContour
                .Select(p => new PointF(p.X, p.Y))
                .ToArray();
            
            if (points.Length >= 3)
            {
                // 填充轮廓区域
                ctx.Fill(Color.White, new Polygon(points));
                
                // 绘制轮廓边界
                ctx.Draw(Color.Gray, 2, new Polygon(points));
            }
            
            // 绘制内轮廓（如天井等）
            foreach (var innerContour in normalizedContour.InnerContours)
            {
                if (innerContour.Count >= 3)
                {
                    var innerPoints = innerContour
                        .Select(p => new PointF(p.X, p.Y))
                        .ToArray();
                    
                    // 内轮廓用黑色填充（表示空洞）
                    ctx.Fill(Color.Black, new Polygon(innerPoints));
                    ctx.Draw(Color.Gray, 2, new Polygon(innerPoints));
                }
            }
        });
        
        return image;
    }
    
    public torch.Tensor PreprocessImage(Image<Rgba32> image)
    {
        var device = DeviceManager.DefaultDevice;
        
        // 转换为灰度并归一化到 [-1, 1]
        var width = image.Width;
        var height = image.Height;
        var data = new float[width * height];
        
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                var pixel = image[x, y];
                // 转换为灰度值并归一化到 [-1, 1]
                var gray = (pixel.R * 0.299f + pixel.G * 0.587f + pixel.B * 0.114f) / 255.0f;
                data[y * width + x] = gray * 2.0f - 1.0f;
            }
        }
        
        // 创建张量 [1, 1, H, W]
        var tensor = torch.tensor(data, dtype: torch.float32, device: device)
            .view(1, 1, height, width);
        
        return tensor;
    }
    
    public Image<Rgba32> PostprocessTensor(torch.Tensor tensor)
    {
        // 移动到CPU并转换为数组
        var cpuTensor = tensor.cpu();
        
        // 假设张量形状为 [1, 1, H, W] 或 [1, H, W]
        var shape = cpuTensor.shape;
        int height, width;
        
        if (shape.Length == 4)
        {
            height = (int)shape[2];
            width = (int)shape[3];
            cpuTensor = cpuTensor.squeeze(0).squeeze(0); // 移除批次和通道维度
        }
        else if (shape.Length == 3)
        {
            height = (int)shape[1];
            width = (int)shape[2];
            cpuTensor = cpuTensor.squeeze(0); // 移除批次维度
        }
        else
        {
            height = (int)shape[0];
            width = (int)shape[1];
        }
        
        var data = cpuTensor.data<float>().ToArray();
        var image = new Image<Rgba32>(width, height);
        
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                // 从 [-1, 1] 反归一化到 [0, 255]
                var value = (data[y * width + x] + 1.0f) / 2.0f;
                value = Math.Clamp(value, 0.0f, 1.0f);
                var gray = (byte)(value * 255);
                
                image[x, y] = new Rgba32(gray, gray, gray, 255);
            }
        }
        
        return image;
    }
}
