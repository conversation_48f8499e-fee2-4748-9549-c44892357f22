using FloorPlanGAN.Core;
using FloorPlanGAN.Core.Configuration;
using FloorPlanGAN.Data.Models;
using FloorPlanGAN.Models;
using System.Drawing;

namespace FloorPlanGAN.Examples;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("FloorPlanGAN 示例程序");
        Console.WriteLine("===================");
        
        // 初始化设备
        DeviceManager.Initialize();
        
        // 创建示例项目
        await CreateSampleProject();
        
        // 演示模型训练（模拟）
        await DemonstrateTraining();
        
        // 演示生成过程（模拟）
        await DemonstrateGeneration();
        
        Console.WriteLine("\n示例程序完成！");
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }
    
    static async Task CreateSampleProject()
    {
        Console.WriteLine("\n1. 创建示例项目");
        Console.WriteLine("================");
        
        // 创建建筑项目
        var project = new BuildingProject
        {
            ProjectName = "示例住宅楼",
            Description = "一个3层住宅楼的平面图生成示例",
            FloorCount = 3
        };
        
        // 创建楼层轮廓
        for (int floor = 1; floor <= 3; floor++)
        {
            var contour = CreateSampleContour(floor);
            project.AddFloorContour(contour);
            
            Console.WriteLine($"  - 已添加第{floor}层轮廓，面积: {contour.Area:F2} 平方米");
        }
        
        // 验证项目
        var validation = project.Validate();
        if (validation.IsValid)
        {
            Console.WriteLine("  ✓ 项目验证通过");
        }
        else
        {
            Console.WriteLine("  ✗ 项目验证失败:");
            foreach (var error in validation.Errors)
            {
                Console.WriteLine($"    - {error}");
            }
        }
        
        // 保存项目
        var projectPath = Path.Combine("output", "sample_project.fpg");
        Directory.CreateDirectory(Path.GetDirectoryName(projectPath)!);
        project.SaveToFile(projectPath);
        Console.WriteLine($"  ✓ 项目已保存到: {projectPath}");
    }
    
    static FloorContour CreateSampleContour(int floorNumber)
    {
        var contour = new FloorContour
        {
            FloorNumber = floorNumber,
            FloorName = $"{floorNumber}层",
            FloorType = FloorType.Residential
        };
        
        // 创建一个矩形轮廓 (12m x 8m)
        contour.OuterContour = new List<PointF>
        {
            new PointF(0, 0),
            new PointF(12, 0),
            new PointF(12, 8),
            new PointF(0, 8)
        };
        
        // 如果是第2层，添加一个内轮廓（天井）
        if (floorNumber == 2)
        {
            contour.InnerContours.Add(new List<PointF>
            {
                new PointF(4, 2),
                new PointF(8, 2),
                new PointF(8, 6),
                new PointF(4, 6)
            });
        }
        
        // 计算面积和周长
        contour.CalculateArea();
        contour.CalculatePerimeter();
        contour.CalculateBoundingBox();
        
        return contour;
    }
    
    static async Task DemonstrateTraining()
    {
        Console.WriteLine("\n2. 模型训练演示");
        Console.WriteLine("================");
        
        var config = new TrainingConfig
        {
            BatchSize = 16,
            LearningRate = 0.0002f,
            Epochs = 10, // 演示用较少的轮数
            SaveInterval = 5
        };
        
        Console.WriteLine($"  训练配置:");
        Console.WriteLine($"    - 批次大小: {config.BatchSize}");
        Console.WriteLine($"    - 学习率: {config.LearningRate}");
        Console.WriteLine($"    - 训练轮数: {config.Epochs}");
        
        // 模拟训练过程
        Console.WriteLine("\n  开始训练...");
        for (int epoch = 1; epoch <= config.Epochs; epoch++)
        {
            // 模拟训练时间
            await Task.Delay(500);
            
            // 模拟损失值
            var dLoss = 0.5f + (float)(new Random().NextDouble() * 0.3 - 0.15);
            var gLoss = 0.8f + (float)(new Random().NextDouble() * 0.4 - 0.2);
            
            Console.WriteLine($"    Epoch {epoch}/{config.Epochs} - D Loss: {dLoss:F4}, G Loss: {gLoss:F4}");
            
            if (epoch % config.SaveInterval == 0)
            {
                Console.WriteLine($"    ✓ 模型检查点已保存 (epoch_{epoch})");
            }
        }
        
        Console.WriteLine("  ✓ 训练完成！");
    }
    
    static async Task DemonstrateGeneration()
    {
        Console.WriteLine("\n3. 平面图生成演示");
        Console.WriteLine("==================");
        
        // 创建示例轮廓
        var contour = CreateSampleContour(1);
        Console.WriteLine($"  输入轮廓: {contour.FloorName}");
        Console.WriteLine($"    - 面积: {contour.Area:F2} 平方米");
        Console.WriteLine($"    - 周长: {contour.Perimeter:F2} 米");
        
        // 模拟生成过程
        Console.WriteLine("\n  正在生成平面图...");
        
        for (int i = 1; i <= 5; i++)
        {
            await Task.Delay(300);
            
            var progress = i * 20;
            Console.Write($"\r    进度: {progress}% ");
            
            // 显示进度条
            var progressBar = new string('█', progress / 5) + new string('░', 20 - progress / 5);
            Console.Write($"[{progressBar}]");
        }
        
        Console.WriteLine("\n  ✓ 生成完成！");
        
        // 模拟生成结果
        var generatedPlan = new GeneratedFloorPlan
        {
            FloorNumber = contour.FloorNumber,
            ImagePath = Path.Combine("output", "generated", $"floor_{contour.FloorNumber}_sample.png"),
            GeneratedAt = DateTime.Now,
            QualityScore = 0.85f,
            UserRating = 4,
            Parameters = new GenerationParameters
            {
                ModelName = "FloorPlanGAN",
                Seed = 42,
                Steps = 50,
                GuidanceScale = 7.5f
            }
        };
        
        Console.WriteLine($"  生成结果:");
        Console.WriteLine($"    - 文件路径: {generatedPlan.ImagePath}");
        Console.WriteLine($"    - 质量评分: {generatedPlan.QualityScore:F2}");
        Console.WriteLine($"    - 生成时间: {generatedPlan.GeneratedAt:yyyy-MM-dd HH:mm:ss}");
        Console.WriteLine($"    - 随机种子: {generatedPlan.Parameters.Seed}");
        
        // 创建输出目录
        Directory.CreateDirectory(Path.GetDirectoryName(generatedPlan.ImagePath)!);
        
        // 模拟保存图像文件
        await File.WriteAllTextAsync(generatedPlan.ImagePath, "# 这是一个模拟的平面图文件\n生成时间: " + DateTime.Now);
        Console.WriteLine("  ✓ 结果已保存");
    }
}
