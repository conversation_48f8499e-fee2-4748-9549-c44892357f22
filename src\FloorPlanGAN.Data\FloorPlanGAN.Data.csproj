<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <Title>FloorPlanGAN.Data</Title>
    <Description>FloorPlanGAN数据处理模块，负责轮廓解析、图像预处理等功能</Description>
    <Authors>FloorPlanGAN Team</Authors>
    <Version>1.0.0</Version>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="TorchSharp" Version="0.103.1" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.6" />
    <PackageReference Include="SixLabors.ImageSharp.Drawing" Version="2.1.5" />
    <PackageReference Include="SkiaSharp" Version="2.88.8" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Svg" Version="3.4.7" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FloorPlanGAN.Core\FloorPlanGAN.Core.csproj" />
  </ItemGroup>

</Project>
