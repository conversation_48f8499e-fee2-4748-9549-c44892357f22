﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\src\Maomi.Torch\Maomi.Torch.csproj" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="TorchAudio" Version="0.105.0" />
		<PackageReference Include="TorchSharp-cuda-windows" Version="0.105.0" />
	</ItemGroup>

	<ItemGroup>
	  <None Update="steam-train-whistle-daniel_simon.wav">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	</ItemGroup>

</Project>
