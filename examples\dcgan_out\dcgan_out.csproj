﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\src\Maomi.Torch\Maomi.Torch.csproj" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="TorchSharp-cuda-windows" Version="0.103.1" />
	</ItemGroup>

  <ItemGroup>
    <Compile Include="..\dcgan\Discriminator.cs" Link="Discriminator.cs" />
    <Compile Include="..\dcgan\Generator.cs" Link="Generator.cs" />
    <Compile Include="..\dcgan\Options.cs" Link="Options.cs" />
  </ItemGroup>

</Project>
