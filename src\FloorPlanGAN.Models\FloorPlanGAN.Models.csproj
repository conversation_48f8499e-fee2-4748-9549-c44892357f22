<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <Title>FloorPlanGAN.Models</Title>
    <Description>FloorPlanGAN模型库，包含生成器、判别器和训练逻辑</Description>
    <Authors>FloorPlanGAN Team</Authors>
    <Version>1.0.0</Version>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="TorchSharp" Version="0.103.1" />
    <PackageReference Include="TorchSharp.PyBridge" Version="1.4.3" />
    <PackageReference Include="TorchVision" Version="0.103.1" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.5" />
    <PackageReference Include="SixLabors.ImageSharp.Drawing" Version="2.1.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FloorPlanGAN.Core\FloorPlanGAN.Core.csproj" />
    <ProjectReference Include="..\FloorPlanGAN.Data\FloorPlanGAN.Data.csproj" />
    <ProjectReference Include="..\..\..\src\Maomi.Torch\Maomi.Torch.csproj" />
  </ItemGroup>

</Project>
