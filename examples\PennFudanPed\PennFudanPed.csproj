﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\src\Maomi.Torch\Maomi.Torch.csproj" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="TorchSharp-cuda-windows" Version="0.103.1" />
	</ItemGroup>

</Project>
