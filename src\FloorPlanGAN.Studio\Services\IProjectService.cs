using FloorPlanGAN.Data.Models;

namespace FloorPlanGAN.Studio.Services;

/// <summary>
/// 项目服务接口
/// </summary>
public interface IProjectService
{
    /// <summary>
    /// 创建新项目
    /// </summary>
    Task<BuildingProject> CreateNewProjectAsync(string projectName, string description = "");
    
    /// <summary>
    /// 加载项目
    /// </summary>
    Task<BuildingProject?> LoadProjectAsync(string filePath);
    
    /// <summary>
    /// 保存项目
    /// </summary>
    Task SaveProjectAsync(BuildingProject project, string? filePath = null);
    
    /// <summary>
    /// 另存为项目
    /// </summary>
    Task SaveProjectAsAsync(BuildingProject project, string filePath);
    
    /// <summary>
    /// 验证项目
    /// </summary>
    ValidationResult ValidateProject(BuildingProject project);
    
    /// <summary>
    /// 获取最近打开的项目列表
    /// </summary>
    Task<List<string>> GetRecentProjectsAsync();
    
    /// <summary>
    /// 添加到最近打开的项目
    /// </summary>
    Task AddToRecentProjectsAsync(string filePath);
}

/// <summary>
/// 项目服务实现
/// </summary>
public class ProjectService : IProjectService
{
    private const string RecentProjectsFile = "recent_projects.json";
    private const int MaxRecentProjects = 10;
    
    public async Task<BuildingProject> CreateNewProjectAsync(string projectName, string description = "")
    {
        return await Task.FromResult(new BuildingProject
        {
            ProjectName = projectName,
            Description = description,
            CreatedAt = DateTime.Now,
            LastModified = DateTime.Now
        });
    }
    
    public async Task<BuildingProject?> LoadProjectAsync(string filePath)
    {
        if (!File.Exists(filePath))
            throw new FileNotFoundException($"项目文件不存在: {filePath}");
        
        return await Task.Run(() =>
        {
            var project = BuildingProject.LoadFromFile(filePath);
            if (project != null)
            {
                // 添加到最近打开的项目
                _ = AddToRecentProjectsAsync(filePath);
            }
            return project;
        });
    }
    
    public async Task SaveProjectAsync(BuildingProject project, string? filePath = null)
    {
        await Task.Run(() =>
        {
            if (string.IsNullOrEmpty(filePath))
            {
                // 如果没有指定路径，使用项目名称作为文件名
                var fileName = $"{project.ProjectName}.fpg";
                filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), 
                                       "FloorPlanGAN", "Projects", fileName);
            }
            
            project.SaveToFile(filePath);
        });
        
        if (!string.IsNullOrEmpty(filePath))
        {
            await AddToRecentProjectsAsync(filePath);
        }
    }
    
    public async Task SaveProjectAsAsync(BuildingProject project, string filePath)
    {
        await Task.Run(() =>
        {
            project.SaveToFile(filePath);
        });
        
        await AddToRecentProjectsAsync(filePath);
    }
    
    public ValidationResult ValidateProject(BuildingProject project)
    {
        return project.Validate();
    }
    
    public async Task<List<string>> GetRecentProjectsAsync()
    {
        return await Task.Run(() =>
        {
            var recentFile = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "FloorPlanGAN", RecentProjectsFile);
            
            if (!File.Exists(recentFile))
                return new List<string>();
            
            try
            {
                var json = File.ReadAllText(recentFile);
                var projects = Newtonsoft.Json.JsonConvert.DeserializeObject<List<string>>(json) ?? new List<string>();
                
                // 过滤掉不存在的文件
                return projects.Where(File.Exists).ToList();
            }
            catch
            {
                return new List<string>();
            }
        });
    }
    
    public async Task AddToRecentProjectsAsync(string filePath)
    {
        await Task.Run(() =>
        {
            var appDataDir = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "FloorPlanGAN");
            
            Directory.CreateDirectory(appDataDir);
            
            var recentFile = Path.Combine(appDataDir, RecentProjectsFile);
            
            var recentProjects = new List<string>();
            
            if (File.Exists(recentFile))
            {
                try
                {
                    var json = File.ReadAllText(recentFile);
                    recentProjects = Newtonsoft.Json.JsonConvert.DeserializeObject<List<string>>(json) ?? new List<string>();
                }
                catch
                {
                    // 忽略解析错误，使用空列表
                }
            }
            
            // 移除已存在的项目路径
            recentProjects.Remove(filePath);
            
            // 添加到列表开头
            recentProjects.Insert(0, filePath);
            
            // 限制最大数量
            if (recentProjects.Count > MaxRecentProjects)
            {
                recentProjects = recentProjects.Take(MaxRecentProjects).ToList();
            }
            
            // 保存到文件
            var updatedJson = Newtonsoft.Json.JsonConvert.SerializeObject(recentProjects, Newtonsoft.Json.Formatting.Indented);
            File.WriteAllText(recentFile, updatedJson);
        });
    }
}
