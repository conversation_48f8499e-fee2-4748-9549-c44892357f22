using TorchSharp;
using TorchSharp.Modules;
using static TorchSharp.torch;
using static TorchSharp.torch.optim;
using FloorPlanGAN.Core;
using FloorPlanGAN.Core.Configuration;

namespace FloorPlanGAN.Models;

/// <summary>
/// GAN训练器
/// </summary>
public class FloorPlanGANTrainer
{
    private readonly FloorPlanGenerator _generator;
    private readonly FloorPlanDiscriminator _discriminator;
    private readonly Adam _generatorOptimizer;
    private readonly Adam _discriminatorOptimizer;
    private readonly TrainingConfig _config;
    private readonly Device _device;
    
    // 训练统计
    public TrainingStatistics Statistics { get; private set; } = new();
    
    // 事件
    public event Action<int, TrainingStatistics>? EpochCompleted;
    public event Action<int, int, float, float>? BatchCompleted;
    public event Action<string>? LogMessage;
    
    public FloorPlanGANTrainer(TrainingConfig config)
    {
        _config = config;
        _device = DeviceManager.DefaultDevice;
        
        // 初始化模型
        _generator = new FloorPlanGenerator(
            noiseSize: 100,
            conditionSize: 256,
            imageSize: 512,
            features: 64
        );
        
        _discriminator = new FloorPlanDiscriminator(
            imageSize: 512,
            features: 64
        );
        
        // 移动到设备
        _generator.to(_device);
        _discriminator.to(_device);
        
        // 初始化优化器
        _generatorOptimizer = Adam(
            _generator.parameters(),
            lr: config.LearningRate,
            betas: (config.Beta1, config.Beta2)
        );
        
        _discriminatorOptimizer = Adam(
            _discriminator.parameters(),
            lr: config.LearningRate,
            betas: (config.Beta1, config.Beta2)
        );
        
        LogMessage?.Invoke($"训练器初始化完成，使用设备: {_device}");
    }
    
    /// <summary>
    /// 训练一个批次
    /// </summary>
    private (float dLoss, float gLoss) TrainBatch(Tensor realImages, Tensor contours)
    {
        var batchSize = realImages.shape[0];
        
        // ==================== 训练判别器 ====================
        _discriminatorOptimizer.zero_grad();
        
        // 真实样本损失
        var realLoss = _discriminator.CalculateRealLoss(realImages, contours);
        
        // 生成假样本
        var noise = _generator.GenerateNoise(batchSize, _device);
        var fakeImages = _generator.forward(noise, contours);
        
        // 假样本损失
        var fakeLoss = _discriminator.CalculateFakeLoss(fakeImages.detach(), contours);
        
        // 总判别器损失
        var dLoss = (realLoss + fakeLoss) / 2;
        dLoss.backward();
        _discriminatorOptimizer.step();
        
        // ==================== 训练生成器 ====================
        _generatorOptimizer.zero_grad();
        
        // 生成器损失（希望判别器认为生成的图像是真实的）
        var gPredictions = _discriminator.forward(fakeImages, contours);
        var realLabels = torch.ones_like(gPredictions);
        var gLoss = functional.binary_cross_entropy(gPredictions, realLabels);
        
        gLoss.backward();
        _generatorOptimizer.step();
        
        return (dLoss.item<float>(), gLoss.item<float>());
    }
    
    /// <summary>
    /// 训练模型
    /// </summary>
    public async Task TrainAsync(torch.utils.data.DataLoader dataLoader, CancellationToken cancellationToken = default)
    {
        LogMessage?.Invoke("开始训练...");
        Statistics.StartTime = DateTime.Now;
        
        for (int epoch = 0; epoch < _config.Epochs; epoch++)
        {
            if (cancellationToken.IsCancellationRequested) break;
            
            _generator.train();
            _discriminator.train();
            
            float epochDLoss = 0;
            float epochGLoss = 0;
            int batchCount = 0;
            
            foreach (var batch in dataLoader)
            {
                if (cancellationToken.IsCancellationRequested) break;
                
                var realImages = batch["images"].to(_device);
                var contours = batch["contours"].to(_device);
                
                var (dLoss, gLoss) = TrainBatch(realImages, contours);
                
                epochDLoss += dLoss;
                epochGLoss += gLoss;
                batchCount++;
                
                BatchCompleted?.Invoke(epoch, batchCount, dLoss, gLoss);
                
                // 定期清理内存
                if (batchCount % 10 == 0)
                {
                    DeviceManager.ClearCache();
                }
            }
            
            // 计算平均损失
            epochDLoss /= batchCount;
            epochGLoss /= batchCount;
            
            // 更新统计信息
            Statistics.CurrentEpoch = epoch + 1;
            Statistics.DiscriminatorLoss = epochDLoss;
            Statistics.GeneratorLoss = epochGLoss;
            Statistics.TotalBatches += batchCount;
            
            LogMessage?.Invoke($"Epoch {epoch + 1}/{_config.Epochs} - D Loss: {epochDLoss:F4}, G Loss: {epochGLoss:F4}");
            
            // 保存模型
            if ((epoch + 1) % _config.SaveInterval == 0)
            {
                await SaveModelsAsync($"checkpoint_epoch_{epoch + 1}");
            }
            
            EpochCompleted?.Invoke(epoch + 1, Statistics);
            
            // 让UI有机会更新
            await Task.Delay(1, cancellationToken);
        }
        
        Statistics.EndTime = DateTime.Now;
        LogMessage?.Invoke($"训练完成！总耗时: {Statistics.TrainingDuration:hh\\:mm\\:ss}");
    }
    
    /// <summary>
    /// 保存模型
    /// </summary>
    public async Task SaveModelsAsync(string checkpointName)
    {
        await Task.Run(() =>
        {
            var modelDir = Path.Combine(_config.ModelPath, checkpointName);
            Directory.CreateDirectory(modelDir);
            
            _generator.save(Path.Combine(modelDir, "generator.dat"));
            _discriminator.save(Path.Combine(modelDir, "discriminator.dat"));
            
            // 保存优化器状态
            _generatorOptimizer.save(Path.Combine(modelDir, "generator_optimizer.dat"));
            _discriminatorOptimizer.save(Path.Combine(modelDir, "discriminator_optimizer.dat"));
            
            LogMessage?.Invoke($"模型已保存到: {modelDir}");
        });
    }
    
    /// <summary>
    /// 加载模型
    /// </summary>
    public async Task LoadModelsAsync(string checkpointName)
    {
        await Task.Run(() =>
        {
            var modelDir = Path.Combine(_config.ModelPath, checkpointName);
            
            if (Directory.Exists(modelDir))
            {
                var generatorPath = Path.Combine(modelDir, "generator.dat");
                var discriminatorPath = Path.Combine(modelDir, "discriminator.dat");
                
                if (File.Exists(generatorPath))
                {
                    _generator.load(generatorPath);
                    LogMessage?.Invoke("生成器模型加载成功");
                }
                
                if (File.Exists(discriminatorPath))
                {
                    _discriminator.load(discriminatorPath);
                    LogMessage?.Invoke("判别器模型加载成功");
                }
                
                // 加载优化器状态
                var genOptimizerPath = Path.Combine(modelDir, "generator_optimizer.dat");
                var discOptimizerPath = Path.Combine(modelDir, "discriminator_optimizer.dat");
                
                if (File.Exists(genOptimizerPath))
                {
                    _generatorOptimizer.load(genOptimizerPath);
                }
                
                if (File.Exists(discOptimizerPath))
                {
                    _discriminatorOptimizer.load(discOptimizerPath);
                }
            }
            else
            {
                throw new DirectoryNotFoundException($"模型目录不存在: {modelDir}");
            }
        });
    }
    
    /// <summary>
    /// 生成样本
    /// </summary>
    public Tensor GenerateSample(Tensor contour, int? seed = null)
    {
        _generator.eval();
        return _generator.GenerateFromContour(contour, seed);
    }
    
    /// <summary>
    /// 获取生成器
    /// </summary>
    public FloorPlanGenerator GetGenerator() => _generator;
    
    /// <summary>
    /// 获取判别器
    /// </summary>
    public FloorPlanDiscriminator GetDiscriminator() => _discriminator;
}

/// <summary>
/// 训练统计信息
/// </summary>
public class TrainingStatistics
{
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public int CurrentEpoch { get; set; }
    public int TotalBatches { get; set; }
    public float DiscriminatorLoss { get; set; }
    public float GeneratorLoss { get; set; }
    
    public TimeSpan TrainingDuration => 
        (EndTime ?? DateTime.Now) - StartTime;
    
    public float AverageBatchTime => 
        TotalBatches > 0 ? (float)TrainingDuration.TotalSeconds / TotalBatches : 0;
}
