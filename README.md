# 文档说明

教程名称：使用 C# 入门深度学习

作者：痴者工良

教程地址：

[https://torch.whuanle.cn](https://torch.whuanle.cn)

<br />

电子书仓库：https://github.com/whuanle/cs_pytorch

Maomi.Torch 项目仓库：https://github.com/whuanle/Maomi.Torch

<br />

## 导读

本教程通过使用 C# ，学习数学基础、Pytorch 框架、深度学习等，目前还在编写中。

- 教程中每个小节都有代码示例
- 深入原理，讲解深层知识
- 由易到难，从入门到掌握
- 循序渐进，一步步学习，一步步拓展知识面
- 内容完整、齐全，可以系统式学习
- 大量代码示例和场景实践




### 目录

* [文档导读](https://torch.whuanle.cn/l)
* [第一部分：基础](https://torch.whuanle.cn/01.base/)
  * [第一部分：基础](https://torch.whuanle.cn/01.base/01.env.html)
  * [Pytorch 基础](https://torch.whuanle.cn/01.base/02.base.html)
  * [线性代数基础](https://torch.whuanle.cn/01.base/03.linear.html)
  * [微积分和梯度下降](https://torch.whuanle.cn/01.base/04.higher.html)
  * [概率论基础](https://torch.whuanle.cn/01.base/05.odds.html)
* [第二部分](https://torch.whuanle.cn/02.start/)
  * [神经网络基础知识](https://torch.whuanle.cn/02.start/01.neural_network.html) 
  * [使用神经网络训练模型-案例一](https://torch.whuanle.cn/02.start/02.start_torch.html) 
  * [使用神经网络训练模型-案例二](https://torch.whuanle.cn/02.start/03.xl.html) 
  * [使用预训练模型](https://torch.whuanle.cn/02.start/04.models.html) 
