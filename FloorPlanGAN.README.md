# FloorPlanGAN.Studio

基于生成对抗网络(GAN)的建筑楼层平面布置图自动生成系统

## 项目简介

FloorPlanGAN.Studio 是一个创新的建筑设计辅助工具，利用深度学习技术自动生成建筑楼层的室内平面布置图。用户只需提供楼层的外轮廓和楼层数量，系统即可基于训练好的GAN模型生成合理的室内空间布局。

## 核心功能

- 🏗️ **智能布局生成**: 基于条件GAN生成符合建筑规范的室内布局
- 🎨 **可视化界面**: 直观的WPF界面，支持轮廓绘制和结果预览
- 📐 **多格式支持**: 支持DXF、SVG、JSON等多种轮廓数据格式
- 🔄 **实时生成**: 快速响应，实时预览生成结果
- 💾 **模型训练**: 支持自定义数据集训练和模型优化
- 📊 **批量处理**: 支持多楼层同时生成和批量导出

## 技术架构

### 核心组件

- **FloorPlanGAN.Core**: 核心功能库，包含设备管理、工具函数等
- **FloorPlanGAN.Models**: GAN模型实现，包含生成器和判别器
- **FloorPlanGAN.Data**: 数据处理模块，负责轮廓解析和图像预处理
- **FloorPlanGAN.Studio**: WPF主应用程序，提供用户界面

### 技术栈

- **深度学习框架**: TorchSharp + Maomi.Torch
- **用户界面**: WPF + MVVM
- **图形处理**: SkiaSharp
- **数据格式**: 支持CAD标准格式

## 快速开始

### 环境要求

- .NET 8.0 或更高版本
- Windows 10/11
- 推荐使用NVIDIA GPU（支持CUDA）

### 使用指南

1. **设置楼层数量**: 在界面中输入要生成的楼层数量
2. **导入轮廓数据**: 绘制或导入各楼层的外轮廓
3. **生成布局**: 点击生成按钮，系统将自动生成室内布局
4. **预览和调整**: 查看生成结果，可进行微调
5. **导出结果**: 将满意的布局导出为标准格式

## 项目结构

```
FloorPlanGAN.Studio/
├── src/
│   ├── FloorPlanGAN.Core/          # 核心功能库
│   ├── FloorPlanGAN.Models/        # GAN模型实现
│   ├── FloorPlanGAN.Data/          # 数据处理模块
│   └── FloorPlanGAN.Studio/        # WPF主应用
├── examples/
│   └── FloorPlanGAN.Examples/      # 示例和测试
└── data/                          # 示例数据
```
