using TorchSharp;
using static TorchSharp.torch;
using Maomi.Torch;

namespace FloorPlanGAN.Core;

/// <summary>
/// 设备管理器，负责GPU/CPU设备的选择和管理
/// </summary>
public static class DeviceManager
{
    private static Device? _defaultDevice;
    
    /// <summary>
    /// 获取默认设备
    /// </summary>
    public static Device DefaultDevice
    {
        get
        {
            _defaultDevice ??= MM.GetOptimalDevice();
            return _defaultDevice;
        }
    }
    
    /// <summary>
    /// 初始化设备并设置为默认设备
    /// </summary>
    public static void Initialize()
    {
        var device = DefaultDevice;
        torch.set_default_device(device);
        
        Console.WriteLine($"FloorPlanGAN 正在使用设备: {device}");
        
        if (device.type == DeviceType.CUDA)
        {
            Console.WriteLine($"CUDA 设备信息: {torch.cuda.get_device_name(device.index)}");
            Console.WriteLine($"CUDA 内存: {torch.cuda.get_device_properties(device.index).total_memory / 1024 / 1024 / 1024:F1} GB");
        }
    }
    
    /// <summary>
    /// 检查是否有可用的GPU
    /// </summary>
    public static bool HasGPU => torch.cuda.is_available();
    
    /// <summary>
    /// 检查是否有可用的MPS（Apple Silicon）
    /// </summary>
    public static bool HasMPS => torch.mps_is_available();
    
    /// <summary>
    /// 获取设备信息字符串
    /// </summary>
    public static string GetDeviceInfo()
    {
        var device = DefaultDevice;
        var info = $"设备类型: {device.type}";
        
        if (device.type == DeviceType.CUDA)
        {
            info += $"\nGPU 名称: {torch.cuda.get_device_name(device.index)}";
            info += $"\nGPU 内存: {torch.cuda.get_device_properties(device.index).total_memory / 1024 / 1024 / 1024:F1} GB";
        }
        else if (device.type == DeviceType.MPS)
        {
            info += "\nApple Silicon GPU (MPS)";
        }
        else
        {
            info += "\nCPU 计算";
        }
        
        return info;
    }
    
    /// <summary>
    /// 清理GPU内存
    /// </summary>
    public static void ClearCache()
    {
        if (torch.cuda.is_available())
        {
            torch.cuda.empty_cache();
            GC.Collect();
            Console.WriteLine("GPU 内存缓存已清理");
        }
    }
}
