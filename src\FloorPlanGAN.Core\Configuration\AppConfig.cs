using Newtonsoft.Json;

namespace FloorPlanGAN.Core.Configuration;

/// <summary>
/// 应用程序配置
/// </summary>
public class AppConfig
{
    /// <summary>
    /// 模型配置
    /// </summary>
    public ModelConfig Model { get; set; } = new();
    
    /// <summary>
    /// 训练配置
    /// </summary>
    public TrainingConfig Training { get; set; } = new();
    
    /// <summary>
    /// 界面配置
    /// </summary>
    public UIConfig UI { get; set; } = new();
    
    /// <summary>
    /// 数据配置
    /// </summary>
    public DataConfig Data { get; set; } = new();
    
    /// <summary>
    /// 从文件加载配置
    /// </summary>
    public static AppConfig LoadFromFile(string filePath)
    {
        if (!File.Exists(filePath))
        {
            var defaultConfig = new AppConfig();
            defaultConfig.SaveToFile(filePath);
            return defaultConfig;
        }
        
        var json = File.ReadAllText(filePath);
        return JsonConvert.DeserializeObject<AppConfig>(json) ?? new AppConfig();
    }
    
    /// <summary>
    /// 保存配置到文件
    /// </summary>
    public void SaveToFile(string filePath)
    {
        var directory = Path.GetDirectoryName(filePath);
        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }
        
        var json = JsonConvert.SerializeObject(this, Formatting.Indented);
        File.WriteAllText(filePath, json);
    }
}

/// <summary>
/// 模型配置
/// </summary>
public class ModelConfig
{
    /// <summary>
    /// 图像尺寸
    /// </summary>
    public int ImageSize { get; set; } = 512;
    
    /// <summary>
    /// 噪声维度
    /// </summary>
    public int NoiseSize { get; set; } = 100;
    
    /// <summary>
    /// 生成器特征数
    /// </summary>
    public int GeneratorFeatures { get; set; } = 64;
    
    /// <summary>
    /// 判别器特征数
    /// </summary>
    public int DiscriminatorFeatures { get; set; } = 64;
    
    /// <summary>
    /// 条件向量维度（楼层轮廓编码）
    /// </summary>
    public int ConditionSize { get; set; } = 256;
}

/// <summary>
/// 训练配置
/// </summary>
public class TrainingConfig
{
    /// <summary>
    /// 批次大小
    /// </summary>
    public int BatchSize { get; set; } = 16;
    
    /// <summary>
    /// 学习率
    /// </summary>
    public float LearningRate { get; set; } = 0.0002f;
    
    /// <summary>
    /// Beta1参数
    /// </summary>
    public float Beta1 { get; set; } = 0.5f;
    
    /// <summary>
    /// Beta2参数
    /// </summary>
    public float Beta2 { get; set; } = 0.999f;
    
    /// <summary>
    /// 训练轮数
    /// </summary>
    public int Epochs { get; set; } = 1000;
    
    /// <summary>
    /// 保存间隔
    /// </summary>
    public int SaveInterval { get; set; } = 100;
    
    /// <summary>
    /// 判别器训练次数比例
    /// </summary>
    public int DiscriminatorSteps { get; set; } = 1;
    
    /// <summary>
    /// 生成器训练次数比例
    /// </summary>
    public int GeneratorSteps { get; set; } = 1;
}

/// <summary>
/// 界面配置
/// </summary>
public class UIConfig
{
    /// <summary>
    /// 窗口宽度
    /// </summary>
    public int WindowWidth { get; set; } = 1200;
    
    /// <summary>
    /// 窗口高度
    /// </summary>
    public int WindowHeight { get; set; } = 800;
    
    /// <summary>
    /// 是否最大化窗口
    /// </summary>
    public bool IsMaximized { get; set; } = false;
    
    /// <summary>
    /// 主题
    /// </summary>
    public string Theme { get; set; } = "Light";
    
    /// <summary>
    /// 语言
    /// </summary>
    public string Language { get; set; } = "zh-CN";
}

/// <summary>
/// 数据配置
/// </summary>
public class DataConfig
{
    /// <summary>
    /// 数据集路径
    /// </summary>
    public string DatasetPath { get; set; } = "data/dataset";
    
    /// <summary>
    /// 模型保存路径
    /// </summary>
    public string ModelPath { get; set; } = "models";
    
    /// <summary>
    /// 输出路径
    /// </summary>
    public string OutputPath { get; set; } = "output";
    
    /// <summary>
    /// 支持的图像格式
    /// </summary>
    public string[] SupportedImageFormats { get; set; } = { ".png", ".jpg", ".jpeg", ".bmp" };
    
    /// <summary>
    /// 支持的轮廓格式
    /// </summary>
    public string[] SupportedContourFormats { get; set; } = { ".dxf", ".svg", ".json" };
}
